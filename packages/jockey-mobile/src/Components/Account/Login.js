import {
  ActivityIndicator,
  BackHandler,
  Dimensions,
  Image,
  Pressable,
  ScrollView,
  StyleSheet,
  Switch,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import React, { useEffect, useRef, useState } from 'react';
import RBSheet from 'react-native-raw-bottom-sheet';
import {
  fonts,
  getVerticalPadding,
  heightPixel,
  widthPixel,
} from '../../styles';
import Svg, { Circle, Path } from 'react-native-svg';
import LinearGradient from 'react-native-linear-gradient';
import Ripple from 'react-native-material-ripple';
import OTPInputView from '@twotalltotems/react-native-otp-input';
import Snackbar from 'react-native-snackbar';
import { useNavigation } from '@react-navigation/native';
import ToggleSwitch from 'toggle-switch-react-native';
import { useUser, useWishlistProducts } from '@appmaker-xyz/shopify';
import { getIdFromGID, updateWebWishlist } from '../../utils/Helper';
import Lottie from 'lottie-react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { getSettings, settings } from '../../../config';


const Login = (props) => {
  const isFromCart = props?.currentAction?.params?.isFromCart ?? false;
  const insets = useSafeAreaInsets();

  const loginCallback = props?.currentAction?.params?.loginCallback;

  const bottomSheetRef = useRef(null);
  const [isWhatsAppOptIn, setIsWhatsAppOptIn] = useState(true);
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [isEmailValid, setIsEmailValid] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [isPhoneValid, setIsPhoneValid] = useState(false);
  const otpInputRef = useRef(null);
  const [seconds, setSeconds] = useState(30);
  const [isActive, setIsActive] = useState(false);
  const [otp, setOtp] = useState('');
  const [isShowError, setShowError] = useState(null);
  const [showEmailError, setShowEmailError] = useState(false);
  const [bottomSheetStatus, setBottomSheetStatus] = useState(0);
  const [loading, setLoading] = useState(false);
  const navigation = useNavigation();
  const mobileNumberRef = useRef(phoneNumber);
  const loginDetails = useRef('');
  const { products, isLoading } = useWishlistProducts(props);
  const settings = getSettings();
  const myHeaders = new Headers();
  myHeaders.append('Content-Type', 'application/json');
  myHeaders.append('version', settings.onboard_header_version);
  myHeaders.append('Origin', settings.onboard_header_origin);
  const hashValue = useRef(null);
  const { user } = useUser();

  // Captcha states
  const [captchaText, setCaptchaText] = useState('');
  const [captchaValue, setCaptchaValue] = useState('');
  const [captchaVerified, setCaptchaVerified] = useState(false);
  const [captchaMessage, setCaptchaMessage] = useState('');
  const [showCaptcha, setShowCaptcha] = useState(false);
  const captchaTimeoutRef = useRef(null);

  useEffect(() => {
    const backAction = () => {
      if (bottomSheetStatus === 2) {
        setBottomSheetStatus(0);
        return true;
      } else if (bottomSheetStatus === 1) {
        setOtp('');
        setShowError(3);
        setBottomSheetStatus(0);
        return true;
      } else if (bottomSheetStatus === 3) {
        setBottomSheetStatus(0);
        return true;
      }
      return false;
    };

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );

    return () => backHandler.remove();
  }, [bottomSheetStatus]);

  const callApi = async () => {
    setLoading(true);
    const requestOptions = {
      method: 'GET',
      redirect: 'follow',
      headers: myHeaders,
    };

    try {
      const sentOtpResponse = await fetch(
        `${settings.onboard_base_url}/api/login/send-otp/+91${phoneNumber}?whatsapp=${isWhatsAppOptIn}&&hash=${hashValue.current}`,
        requestOptions,
      );

      const sendOtpResult = await sentOtpResponse.json();
      console.log('----<', sendOtpResult);
      if (sendOtpResult.status === 200) {
        // Snackbar.show({
        //     text: sendOtpResult?.data?.message,
        //     duration: Snackbar.LENGTH_SHORT,
        //     backgroundColor: "green"
        // });
        setBottomSheetStatus(1);
        startTimer();
      }

      if (sendOtpResult.status === 409) {
        // Snackbar.show({
        //     text: sendOtpResult.data.response,
        //     duration: Snackbar.LENGTH_SHORT,
        //     backgroundColor: "green"

        // });
        setBottomSheetStatus(1);
        startTimer();
      }
      if (sendOtpResult.status === 400) {
        setShowError(0);
      }
    } catch (error) {
      console.error('Fetch Error:', error);
      // Snackbar.show({
      //     text: "Something Went Wrong",
      //     duration: Snackbar.LENGTH_SHORT,
      //     backgroundColor: "green"

      // });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    let interval = null;

    if (isActive && seconds > 0) {
      interval = setInterval(() => {
        setSeconds((prevSeconds) => prevSeconds - 1);
      }, 1000);
    } else if (seconds === 0) {
      clearInterval(interval);
      setShowCaptcha(true); // Show captcha when timer ends
      generateCaptcha(); // Generate new captcha
    }

    return () => clearInterval(interval);
  }, [isActive, seconds]);

  // Clear captcha timeout when component unmounts
  useEffect(() => {
    return () => {
      if (captchaTimeoutRef.current) {
        clearTimeout(captchaTimeoutRef.current);
      }
    };
  }, []);

  const startTimer = () => {
    setSeconds(29);
    setIsActive(true);
    setShowCaptcha(false); // Hide captcha when timer starts
    setCaptchaVerified(false); // Reset captcha verification
    setCaptchaValue(''); // Clear captcha input
    setCaptchaMessage(''); // Clear captcha message
  };

  const handleRedirect = async (response) => {
    const loginUrl = response?.data?.response?.loginUrl;
    const customerId = response?.data?.customerId ?? '';

    const multipassToken = loginUrl?.split('/multipass/')[1];

    const raw = JSON.stringify({
      optIn_val: isWhatsAppOptIn,
      customerId: customerId ?? '',
    });

    const requestOptions = {
      method: 'POST',
      headers: myHeaders,
      body: raw,
      redirect: 'follow',
    };

    const consentResponse = await fetch(
      `${settings.onboard_base_url}/api/login/user/consent`,
      requestOptions,
    );

    for (let index = 0; index < products.length; index++) {
      let productId = getIdFromGID(products[index]?.id);
      let variantId = getIdFromGID(
        products[index]?.variants?.edges[0]?.node?.id,
      );
      updateWebWishlist(customerId, productId, variantId);
    }

    if (multipassToken) {
      await props.onAction({
        action: 'LOGIN_USER_MULTIPASS',
        params: {
          multipassToken: multipassToken,
          method: 'otp',
          ignoreLoginRedirect: true,
        },
      });
      if (loginCallback) {
        loginCallback();
        return;
      }
      // if (isFromCart) {
      //     props.onAction(
      //         {
      //             action: 'START_CHECKOUT'
      //         }
      //     )
      //     navigation.goBack();
      //     return;
      // }
      navigation.goBack();
    }
  };

  const handleVerifyOtp = async (otpCode) => {
    setLoading(true);

    const mobileNumber = `+91${mobileNumberRef?.current}`;
    const requestBody = JSON.stringify({
      phone: mobileNumber,
      otp: otpCode || otp,
      shop: settings?.base_url,
    });

    const requestOptions = {
      method: 'POST',
      headers: myHeaders,
      body: requestBody,
      redirect: 'follow',
    };
    console.log('---Test', requestOptions);
    try {
      console.log('---Test22', requestOptions);

      // Send OTP verification request
      const verifyOtpResponse = await fetch(
        `${settings?.onboard_base_url}/api/login/verify-otp`,
        requestOptions,
      );
      const verifyOtpResult = await verifyOtpResponse.json();
      console.log('---TestREs', verifyOtpResponse, verifyOtpResult);
      // Handle the different response scenarios
      const { status, data, error } = verifyOtpResult;

      // Check for successful verification with no customer found
      if (status === 200 && data?.customer?.length === 0) {
        setBottomSheetStatus(2);
        return;
      }

      // Handle specific error responses
      if (status === 400 || (status === 200 && data?.response === 'Invalid')) {
        setShowError(1); // Invalid OTP
        return;
      }

      // Handle case where email doesn't exist
      if (status === 206 && data?.response?.status === 'Email Does not exist') {
        loginDetails.current = verifyOtpResult;
        setBottomSheetStatus(3); // Show bottom sheet for email not found
        return;
      }

      // Default case, assuming a successful login or redirect
      if (status === 200) {
        await handleRedirect(verifyOtpResult);
        return;
      }

      // If no valid status, log the error message
      console.error('Error: Unexpected response', verifyOtpResult);
    } catch (error) {
      console.error('Fetch Error:', error);
      // Optionally show Snackbar or handle failure case
    } finally {
      setLoading(false);
    }
  };

  const handlePhoneNumberChange = (text) => {
    const updatedPhoneNumber = text.replace(/[^0-9]/g, '');
    setPhoneNumber(updatedPhoneNumber);
    mobileNumberRef.current = updatedPhoneNumber;

    if (updatedPhoneNumber.length == 10) {
      setIsPhoneValid(true);
    } else {
      setIsPhoneValid(false);
    }
  };
  const handleLogin = () => {
    callApi();
  };
  const handleResendOtp = async (isWhatsApp = false) => {
    setOtp('');

    const raw = '';

    const requestOptions = {
      method: 'GET',
      body: raw,
      redirect: 'follow',
      headers: myHeaders,
    };

    try {
      const apiUrl = isWhatsApp
        ? `${settings.onboard_base_url}/api/login/resend-otp/+91${phoneNumber}?whatsapp=true`
        : `${settings.onboard_base_url}/api/login/resend-otp/+91${phoneNumber}`;

      const loginResponse = await fetch(apiUrl, requestOptions);

      const loginResult = await loginResponse.json();

      if (loginResult.status === 200) {
        // Snackbar.show({
        //     text: loginResult?.data?.message,
        //     duration: Snackbar.LENGTH_SHORT,
        //     backgroundColor: "green"
        // });
        startTimer();
      }
    } catch (error) {
      console.error('Fetch Error:', error);
      // Snackbar.show({
      //     text: "Something Went Wrong",
      //     duration: Snackbar.LENGTH_SHORT,
      //     backgroundColor: "green"

      // });
    }
  };

  // Function to generate random captcha text
  const generateCaptcha = () => {
    const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789';
    let captcha = '';
    for (let i = 0; i < 6; i++) {
      captcha += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    setCaptchaText(captcha);
    setCaptchaValue(''); // Clear previous input
    setCaptchaVerified(false); // Reset verification status
    setCaptchaMessage(''); // Clear message

    // Clear any existing timeout
    if (captchaTimeoutRef.current) {
      clearTimeout(captchaTimeoutRef.current);
      captchaTimeoutRef.current = null;
    }
  };

  // Function to verify captcha when full length is entered
  const verifyCaptcha = (text) => {
    // Clear any existing timeout
    if (captchaTimeoutRef.current) {
      clearTimeout(captchaTimeoutRef.current);
    }

    // Only verify when captcha is complete (same length as generated captcha)
    if (text.length === captchaText.length) {
      if (text === captchaText) {
        setCaptchaVerified(true);
        setCaptchaMessage('Captcha verified');
      } else {
        setCaptchaVerified(false);
        setCaptchaMessage('Invalid captcha');
      }
    } else if (text.length === 0) {
      // Clear message when input is empty
      setCaptchaMessage('');
      setCaptchaVerified(false);
    } else {
      // Don't show any message while user is still typing
      setCaptchaMessage('');
      setCaptchaVerified(false);
    }
  };

  // Function to handle resend button click (only when captcha is verified)
  const handleResendButtonClick = async (isWhatsApp) => {
    if (!captchaVerified) return;

    await handleResendOtp(isWhatsApp);

    // Reset captcha state after successful send
    setShowCaptcha(false);
    setCaptchaValue('');
    setCaptchaVerified(false);
    setCaptchaMessage('');
  };

  const openWebview = (url, title) => {
    props.onAction({
      action: 'OPEN_WEBVIEW',
      params: {
        url: url,
        title: title,
        replacePage: false,
      },
    });
  };

  const getBottomLinks = () => {
    return (
      <Text style={styles.legalText}>
        By proceeding, you agree to Jockey India's{' '}
        <Text
          style={styles.linkText}
          onPress={() => {
            openWebview(
              'https://www.jockey.in/pages/privacy-policy',
              'Privacy Policy',
            );
          }}>
          Privacy Policy
        </Text>{' '}
        and{' '}
        <Text
          style={styles.linkText}
          onPress={() => {
            openWebview(
              'https://www.jockey.in/pages/terms-of-use',
              'Terms of Use',
            );
          }}>
          Terms of Use
        </Text>
      </Text>
    );
  };

  const getPrimaryButton = (title, disabled, onPress) => {
    return (
      <LinearGradient
        colors={['#221f20', '#505050']}
        start={{ x: 1, y: 0 }} // Adjusted for 276 degrees
        end={{ x: 0, y: 1 }}
        style={styles.otpButton}>
        <Pressable
          style={{
            height: '100%',
            width: '100%',
            justifyContent: 'center',
            alignItems: 'center',
          }}
          onPress={onPress}
          disabled={disabled}>
          {loading ? (
            <View style={{ height: '100%', width: '100%' }}>
              <Lottie
                source={require('../../assets/lottie/LoadingAnimationJockey.json')}
                autoPlay
                loop
              />
            </View>
          ) : (
            <Text style={styles.otpButtonText}>{title}</Text>
          )}
        </Pressable>
      </LinearGradient>
    );
  };

  const getSecondaryButton = (title) => {
    return (
      <LinearGradient
        colors={['rgba(34, 31, 32, 0.4)', 'rgba(80, 80, 80, 0.4)']}
        start={{ x: 1, y: 0 }} // Adjusted for 276 degrees
        end={{ x: 0, y: 1 }}
        style={styles.otpDisableButton}>
        <Text style={styles.otpButtonText}>{title}</Text>
      </LinearGradient>
    );
  };

  const loginSheetView = () => {
    return (
      <View
        style={{
          paddingVertical: widthPixel(10),
          paddingHorizontal: widthPixel(20),
        }}>
        <Text
          style={{
            fontSize: fonts._18,
            fontFamily: fonts.FONT_FAMILY.Medium,
            alignSelf: 'center',
            marginBottom: widthPixel(18),
          }}>
          Get comfortable with jockey
        </Text>

        <View style={styles.inputContainer}>
          <Text style={styles.countryCode}>IN (+91)</Text>
          <TextInput
            placeholder="Enter Phone No."
            keyboardType="number-pad"
            style={styles.phoneInput}
            maxLength={10}
            value={phoneNumber}
            onChangeText={handlePhoneNumberChange}
          />
        </View>
        {isShowError == 0 && (
          <Text
            style={{
              color: '#ee3224',
              fontSize: fonts._12,
              fontFamily: fonts.FONT_FAMILY.Regular,
              marginVertical: widthPixel(5),
            }}>
            Please Enter a Valid Phone Number
          </Text>
        )}

        {!isPhoneValid ? (
          <>{getSecondaryButton('SEND OTP VIA SMS')}</>
        ) : (
          <>
            {getPrimaryButton('SEND OTP VIA SMS', !isPhoneValid, () => {
              return isPhoneValid && handleLogin();
            })}
          </>
        )}

        <View style={styles.whatsappOptInContainer}>
          <ToggleSwitch
            isOn={isWhatsAppOptIn}
            onToggle={setIsWhatsAppOptIn}
            offColor={'#d7d7d7'}
            onColor={'#66d184'}
          />

          <Text style={styles.whatsappOptInText}>
            Opt in for Important Updates on WhatsApp
          </Text>
        </View>

        {getBottomLinks()}
      </View>
    );
  };

  const otpView = () => {
    return (
      <View
        style={{
          paddingVertical: widthPixel(10),
          paddingHorizontal: widthPixel(20),
        }}>
        <View style={{ height: heightPixel(29), marginBottom: widthPixel(18) }}>
          <Text
            style={{
              textAlign: 'center',
              fontSize: fonts._18,
              fontFamily: fonts.FONT_FAMILY.Medium,
            }}>
            Let's verify your phone number
          </Text>
        </View>

        <Text
          style={{
            textAlign: 'center',
            fontSize: fonts._14,
            fontFamily: fonts.FONT_FAMILY.Regular,
            color: '#221f20ab',
          }}>
          Please enter the OTP that we have shared with you over SMS on
        </Text>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <Text
            style={{
              fontSize: fonts._15,
              fontFamily: fonts.FONT_FAMILY.Bold,
            }}>{`+91 ${phoneNumber}`}</Text>
          <Ripple
            onPress={() => {
              setBottomSheetStatus(0), setShowError(3), setOtp('');
            }}>
            <Image
              style={{
                width: widthPixel(12),
                height: heightPixel(12),
                marginLeft: widthPixel(7),
              }}
              resizeMode="contain"
              source={{
                uri: 'https://cdn.shopify.com/s/files/1/0753/1056/3627/files/pencil.png?v=1686373256',
              }}
            />
          </Ripple>
        </View>

        <View
          style={{
            height: widthPixel(80),
            justifyContent: 'center',
            alignSelf: 'center',
          }}>
          <OTPInputView
            ref={otpInputRef}
            style={styles.otpContainer}
            pinCount={4}
            codeInputFieldStyle={styles.codeInputField}
            code={otp}
            onCodeChanged={setOtp}
            autoFocusOnLoad={false}
            codeInputHighlightStyle={styles.codeInputHighlight}
            onCodeFilled={(code) => {
              console.log('code:::::', code);
            }}
          />
        </View>

        {isShowError == 1 && (
          <Text
            style={{
              color: '#ee3224',
              fontSize: fonts._12,
              fontFamily: fonts.FONT_FAMILY.Regular,
              marginVertical: widthPixel(5),
              textAlign: 'center',
            }}>
            Please enter correct OTP
          </Text>
        )}

        {seconds !== 0 ? (
          <View>
            <Text
              style={{
                fontSize: fonts._14,
                fontFamily: fonts.FONT_FAMILY.Regular,
                color: '#221f20ab',
                fontWeight: '400',
                marginVertical: widthPixel(5),
                textAlign: 'center',
              }}>
              Resend OTP in
            </Text>
          </View>
        ) : (
          <View>
            {/* Captcha Section - Shows when timer ends */}
            {showCaptcha && (
              <View style={styles.inlineCaptchaContainer}>
                <View style={styles.captchaContainer}>
                  <View style={styles.captchaBox}>
                    <Text style={styles.captchaText}>{captchaText}</Text>
                    <TouchableOpacity
                      style={styles.refreshButton}
                      onPress={generateCaptcha}>
                      <Svg
                        width="16"
                        height="16"
                        viewBox="0 0 20 20"
                        fill="none">
                        <Path
                          d="M15.8333 4.16667V8.33334H11.6667"
                          stroke="#666"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <Path
                          d="M4.16669 15.8333V11.6667H8.33335"
                          stroke="#666"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <Path
                          d="M6.05835 7.50001C6.38614 6.77432 6.8739 6.13864 7.48527 5.64613C8.09664 5.15361 8.81403 4.82064 9.57539 4.67621C10.3367 4.53178 11.1182 4.58077 11.8548 4.81892C12.5915 5.05707 13.2614 5.47673 13.8 6.04168L15.8334 8.33334M4.16669 11.6667L6.20002 13.9583C6.73863 14.5233 7.40853 14.9429 8.14515 15.1811C8.88177 15.4192 9.66328 15.4682 10.4246 15.3238C11.186 15.1794 11.9034 14.8464 12.5148 14.3539C13.1261 13.8614 13.6139 13.2257 13.9417 12.5"
                          stroke="#666"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </Svg>
                    </TouchableOpacity>
                  </View>
                  <View style={styles.captchaInputContainer}>
                    <TextInput
                      style={styles.captchaInput}
                      placeholder="Enter Captcha"
                      value={captchaValue}
                      onChangeText={(text) => {
                        setCaptchaValue(text);
                        verifyCaptcha(text);
                      }}
                      maxLength={6}
                    />
                  </View>
                </View>
                {captchaMessage ? (
                  <Text
                    style={[
                      styles.captchaMessage,
                      captchaVerified
                        ? styles.captchaSuccess
                        : styles.captchaError,
                    ]}>
                    {captchaMessage}
                  </Text>
                ) : null}
              </View>
            )}

            <Text
              style={{
                fontSize: fonts._14,
                fontFamily: fonts.FONT_FAMILY.Regular,
                color: '#428bc1',
                fontWeight: '400',
                marginVertical: widthPixel(2),
                textAlign: 'center',
              }}>
              Resend OTP via
            </Text>
            <View style={styles.resendButtonContainer}>
              <Ripple
                style={[
                  styles.resendButton,
                  !captchaVerified &&
                    showCaptcha &&
                    styles.resendButtonDisabled,
                ]}
                onPress={
                  loading || (!captchaVerified && showCaptcha)
                    ? null
                    : () => handleResendButtonClick(false)
                }
                disabled={loading || (!captchaVerified && showCaptcha)}>
                <Svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <Path
                    d="M20 4H4C2.9 4 2 4.9 2 6V18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V6C22 4.9 21.1 4 20 4ZM20 8L12 13L4 8V6L12 11L20 6V8Z"
                    fill={!captchaVerified && showCaptcha ? '#ccc' : '#428bc1'}
                  />
                </Svg>
                <Text
                  style={[
                    styles.resendButtonText,
                    !captchaVerified &&
                      showCaptcha &&
                      styles.resendButtonTextDisabled,
                  ]}>
                  SMS
                </Text>
              </Ripple>
              <Ripple
                style={[
                  styles.resendButton,
                  !captchaVerified &&
                    showCaptcha &&
                    styles.resendButtonDisabled,
                ]}
                onPress={
                  loading || (!captchaVerified && showCaptcha)
                    ? null
                    : () => handleResendButtonClick(true)
                }
                disabled={loading || (!captchaVerified && showCaptcha)}>
                <Svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <Path
                    d="M12 2C6.48 2 2 6.48 2 12C2 13.89 2.525 15.66 3.44 17.17L2 22L7.12 20.64C8.57 21.41 10.24 21.83 12 21.83C17.52 21.83 22 17.35 22 11.83C22 6.31 17.52 1.83 12 1.83V2ZM16.25 15.61C15.97 16.29 14.82 16.85 14.3 16.93C13.78 17 13.13 16.99 12.42 16.77C11.99 16.66 11.44 16.51 10.73 16.26C7.75 15.21 5.8 12.21 5.65 12.02C5.5 11.83 4.42 10.38 4.42 8.89C4.42 7.4 5.2 6.67 5.48 6.36C5.76 6.05 6.09 5.97 6.29 5.97C6.49 5.97 6.7 5.98 6.88 5.98C7.07 5.99 7.32 5.92 7.57 6.51C7.82 7.12 8.43 8.61 8.5 8.76C8.57 8.91 8.62 9.09 8.52 9.29C8.42 9.49 8.37 9.62 8.22 9.79C8.07 9.96 7.9 10.17 7.76 10.28C7.61 10.43 7.45 10.59 7.63 10.89C7.81 11.19 8.43 12.21 9.35 13.03C10.53 14.08 11.48 14.4 11.78 14.55C12.08 14.7 12.26 14.68 12.44 14.47C12.62 14.26 13.18 13.58 13.38 13.27C13.58 12.96 13.78 13.01 14.06 13.12C14.34 13.23 15.84 13.96 16.14 14.12C16.44 14.28 16.64 14.36 16.71 14.49C16.78 14.62 16.78 15.15 16.54 15.83L16.25 15.61Z"
                    fill={!captchaVerified && showCaptcha ? '#ccc' : '#25D366'}
                  />
                </Svg>
                <Text
                  style={[
                    styles.resendButtonText,
                    !captchaVerified &&
                      showCaptcha &&
                      styles.resendButtonTextDisabled,
                  ]}>
                  WHATSAPP
                </Text>
              </Ripple>
            </View>
          </View>
        )}

        {seconds == 0 ? null : (
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <Text
              style={{
                color: 'black',
                fontSize: fonts._14,
                fontFamily: fonts.FONT_FAMILY.Bold,
                lineHeight: heightPixel(18),
              }}>
              {seconds}
            </Text>
            <Text
              style={{
                color: '#221f20ab',
                fontSize: fonts._14,
                fontFamily: fonts.FONT_FAMILY.Bold,
                lineHeight: heightPixel(18),
              }}>
              s
            </Text>
          </View>
        )}

        {!/^\d{4}$/.test(otp) || (showCaptcha && !captchaVerified) ? (
          <>{getSecondaryButton('VERIFY OTP')}</>
        ) : (
          <>{getPrimaryButton('VERIFY OTP', false, () => handleVerifyOtp())}</>
        )}

        {getBottomLinks()}
      </View>
    );
  };

  const signUpView = () => {
    return (
      <View
        style={{
          paddingVertical: widthPixel(10),
          paddingHorizontal: widthPixel(20),
        }}>
        <View style={{ height: heightPixel(29), marginBottom: widthPixel(18) }}>
          <Text
            style={{
              textAlign: 'center',
              fontSize: fonts._18,
              fontFamily: fonts.FONT_FAMILY.Medium,
            }}>
            Please enter your details to register
          </Text>
        </View>

        <TextInput
          style={styles.signupInputText}
          placeholder="Enter your first name"
          // placeholderTextColor="#221f20"
          // value={firstName}
          onChangeText={setFirstName}
        />

        <TextInput
          style={styles.signupInputText}
          placeholder="Enter your last name"
          // placeholderTextColor="#221f20"
          // value={lastName}
          onChangeText={setLastName}
          // fontFamily="Roboto"
        />

        <TextInput
          style={styles.signupInputText}
          placeholder="Enter your email"
          // placeholderTextColor="#221f20"
          // value={email}
          onChangeText={(text) => handleEmailChange(text.replace(/\s/g, ''))}
          // fontFamily="Roboto"
        />

        {!isFormValid ? (
          <>{getSecondaryButton('SIGN UP')}</>
        ) : (
          <>{getPrimaryButton('SIGN UP', !isFormValid, () => handleSignUp())}</>
        )}

        {getBottomLinks()}
      </View>
    );
  };

  const handleUpdateEmail = async () => {
    setLoading(true);
    const raw = JSON.stringify({
      id: loginDetails?.current?.data?.response?.customer?.id,
      phone: loginDetails?.current?.data?.response?.customer?.phone,
      email: email.trim(),
    });
    const requestOptions = {
      method: 'POST',
      headers: myHeaders,
      body: raw,
      redirect: 'follow',
    };

    try {
      const updateEmailResponse = await fetch(
        `${settings.onboard_base_url}/api/shopify/user/update`,
        requestOptions,
      );

      const updateEmailResults = await updateEmailResponse.json();

      if (updateEmailResults.status === 200) {
        await handleRedirect(updateEmailResults);
      }
      if (
        updateEmailResults.status === 200 &&
        updateEmailResults.data.length === 0
      ) {
        setShowEmailError(true);
        // Snackbar.show({
        //     text: updateEmailResults?.error,
        //     duration: Snackbar.LENGTH_SHORT,
        //     backgroundColor: "red"
        // });
      }
    } catch (error) {
      console.error('Fetch Error:', error);
      // Snackbar.show({
      //     text: "Something Went Wrong",
      //     duration: Snackbar.LENGTH_SHORT,
      //     backgroundColor: "green"

      // });
    } finally {
      setLoading(false);
    }
  };

  const addEmailView = () => {
    return (
      <View
        style={{
          paddingVertical: widthPixel(10),
          paddingHorizontal: widthPixel(20),
        }}>
        <Text
          style={{
            textAlign: 'center',
            fontSize: fonts._18,
            fontFamily: fonts.FONT_FAMILY.Medium,
          }}>
          Please enter your email to update
        </Text>

        <TextInput
          placeholder="Enter your email"
          style={{
            borderWidth: 1,
            borderColor: '#d9d9d9',
            width: '100%',
            height: widthPixel(47),
            textAlign: 'center',
            paddingLeft: 0,
            marginTop: widthPixel(24),
          }}
          onChangeText={handleEmailChange}></TextInput>

        {showEmailError && (
          <Text
            style={{
              color: '#ee3224',
              fontSize: fonts._12,
              fontFamily: fonts.FONT_FAMILY.Regular,
              marginVertical: widthPixel(5),
            }}>
            Email is already Taken
          </Text>
        )}
        {isEmailValid ? (
          <>{getPrimaryButton('SUBMIT', false, () => handleUpdateEmail())}</>
        ) : (
          <>{getSecondaryButton('SUBMIT')}</>
        )}

        {getBottomLinks()}
      </View>
    );
  };

  const validateEmail = (email) => {
    // const emailRegex = /^[^\s<>()[\]\\.,;:@"]+(\.[^\s<>()[\]\\.,;:@"]+)*@[^\s<>()[\]\\.,;:@"]+\.[a-zA-Z]{2,}$/;

    const emailRegex =
      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return emailRegex.test(email);
  };

  const handleEmailChange = (text) => {
    setEmail(text);
    setIsEmailValid(validateEmail(text.trim()));
  };

  const isFormValid = firstName !== '' && lastName !== '' && isEmailValid;

  const handleSignUp = async () => {
    setLoading(true);

    const raw = JSON.stringify({
      FirstName: firstName,
      LastName: lastName,
      EmailId: email,
      MobileNo: `+91${phoneNumber}`,
    });

    const requestOptions = {
      method: 'POST',
      headers: myHeaders,
      body: raw,
      redirect: 'follow',
    };

    try {
      const responseSignUp = await fetch(
        `${settings.onboard_base_url}/api/shopify/register/`,
        requestOptions,
      );

      const responseResult = await responseSignUp.json();

      if (responseResult.status === 200 && responseResult?.data != null) {
        // Snackbar.show({
        //     text: "Signup Successful!",
        //     duration: Snackbar.LENGTH_SHORT,
        //     backgroundColor: "green"
        // });
        await handleRedirect(responseResult);
      } else {
        // Snackbar.show({
        //     text: responseResult?.error,
        //     duration: Snackbar.LENGTH_SHORT,
        //     backgroundColor: "red"
        // });
      }
    } catch (error) {
      console.error('Fetch Error:', error);
      // Snackbar.show({
      //     text: "Something Went Wrong",
      //     duration: Snackbar.LENGTH_SHORT,
      //     backgroundColor: "green"

      // });
    } finally {
      setLoading(false);
    }
  };

  return (
    <ScrollView
      keyboardShouldPersistTaps="handled"
      style={[styles.container, { paddingTop: insets.top }]}>
      <Ripple
        style={styles.closeIcon}
        onPress={() => {
          navigation.goBack();
        }}>
        <Svg
          xmlns="http://www.w3.org/2000/svg"
          width="14"
          height="14"
          viewBox="0 0 14 14">
          <Path
            d="M13.3 0.710224C13.2075 0.61752 13.0976 0.543973 12.9766 0.493791C12.8556 0.44361 12.7259 0.417779 12.595 0.417779C12.464 0.417779 12.3343 0.44361 12.2134 0.493791C12.0924 0.543973 11.9825 0.61752 11.89 0.710224L6.99998 5.59022L2.10998 0.700223C2.0174 0.607642 1.90749 0.534202 1.78652 0.484097C1.66556 0.433992 1.53591 0.408203 1.40498 0.408203C1.27405 0.408203 1.1444 0.433992 1.02344 0.484097C0.902472 0.534202 0.792561 0.607642 0.699979 0.700223C0.607397 0.792805 0.533957 0.902716 0.483852 1.02368C0.433747 1.14464 0.407959 1.27429 0.407959 1.40522C0.407959 1.53615 0.433747 1.6658 0.483852 1.78677C0.533957 1.90773 0.607397 2.01764 0.699979 2.11022L5.58998 7.00022L0.699979 11.8902C0.607397 11.9828 0.533957 12.0927 0.483852 12.2137C0.433747 12.3346 0.407959 12.4643 0.407959 12.5952C0.407959 12.7262 0.433747 12.8558 0.483852 12.9768C0.533957 13.0977 0.607397 13.2076 0.699979 13.3002C0.792561 13.3928 0.902472 13.4662 1.02344 13.5163C1.1444 13.5665 1.27405 13.5922 1.40498 13.5922C1.53591 13.5922 1.66556 13.5665 1.78652 13.5163C1.90749 13.4662 2.0174 13.3928 2.10998 13.3002L6.99998 8.41022L11.89 13.3002C11.9826 13.3928 12.0925 13.4662 12.2134 13.5163C12.3344 13.5665 12.464 13.5922 12.595 13.5922C12.7259 13.5922 12.8556 13.5665 12.9765 13.5163C13.0975 13.4662 13.2074 13.3928 13.3 13.3002C13.3926 13.2076 13.466 13.0977 13.5161 12.9768C13.5662 12.8558 13.592 12.7262 13.592 12.5952C13.592 12.4643 13.5662 12.3346 13.5161 12.2137C13.466 12.0927 13.3926 11.9828 13.3 11.8902L8.40998 7.00022L13.3 2.11022C13.68 1.73022 13.68 1.09022 13.3 0.710224Z"
            fill="rgba(255, 255, 255, 0.5)"
          />
        </Svg>
      </Ripple>

      <LinearGradient
        colors={['#221f20', '#505050']}
        start={{ x: 1, y: 1 }}
        end={{ x: 0, y: 0 }}
        style={{
          alignItems: 'center',
          width: '100%',
          justifyContent: 'center',
          height: heightPixel(190),
          backgroundColor: 'black',
          paddingVertical: widthPixel(10),
          paddingHorizontal: widthPixel(20),
          borderBottomLeftRadius: widthPixel(40),
          borderBottomRightRadius: widthPixel(40),
        }}>
        <View
          style={{
            alignItems: 'center',
            width: '100%',
            height: '100%',
            justifyContent: 'flex-end',
          }}>
          <View
            style={{ justifyContent: 'space-between', gap: heightPixel(24) }}>
            <Image
              style={{
                height: heightPixel(43),
                width: widthPixel(163),
                alignSelf: 'center',
              }}
              source={{
                uri: 'https://cdn.shopify.com/s/files/1/0753/1056/3627/files/Jockey_logo_white.png?v=1702550577',
              }}
            />

            <View>
              <Text
                style={{
                  alignSelf: 'center',
                  fontSize: fonts._18,
                  fontFamily: fonts.FONT_FAMILY.Regular,
                  color: 'white',
                }}>
                THE WORLD OF{' '}
                <Text style={{ fontFamily: fonts.FONT_FAMILY.Bold }}>
                  JOCKEY
                </Text>
              </Text>
              <Text
                style={{
                  alignSelf: 'center',
                  fontSize: fonts._18,
                  fontFamily: fonts.FONT_FAMILY.Regular,
                  color: 'white',
                }}>
                AWAITS YOU
              </Text>
            </View>
          </View>
        </View>
      </LinearGradient>

      {/* {addEmailView()} */}

      {bottomSheetStatus == 0 && loginSheetView()}
      {bottomSheetStatus == 1 && otpView()}
      {bottomSheetStatus == 2 && signUpView()}
      {bottomSheetStatus == 3 && addEmailView()}

      {/* {loading && (
                <View style={{
                    ...StyleSheet.absoluteFillObject,
                    position: 'absolute',
                    height: '100%',
                    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent black background
                    justifyContent: 'center',
                    alignItems: 'center',

                }}>

                    <ActivityIndicator size="large" color="#212121" />

                </View>)
            } */}
    </ScrollView>
  );
};

export default Login;

const styles = StyleSheet.create({
  container: {
    // flex: 1,
    // justifyContent: 'center',
    // alignItems: 'center',
  },
  closeIcon: {
    position: 'absolute',
    backgroundColor: '#505050',
    padding: widthPixel(8),
    zIndex: 100,
    right: widthPixel(20),
    top: heightPixel(10),
    borderRadius: widthPixel(100),
  },
  signupInputText: {
    borderWidth: 1,
    borderColor: '#d9d9d9',
    width: '100%',
    height: widthPixel(47),
    textAlign: 'center',
    paddingLeft: 0,
    marginBottom: widthPixel(20),
  },
  loginButton: {
    padding: widthPixel(10),
    backgroundColor: '#000',
    borderRadius: widthPixel(5),
    marginTop: widthPixel(10),
    height: heightPixel(40),
  },
  loginText: {
    color: '#fff',
    fontSize: 18,
  },
  bottomSheetContainer: {
    borderTopLeftRadius: widthPixel(20),
    borderTopRightRadius: widthPixel(20),
    // padding: 20,
    // maxHeight: heightPixel(600),
    // minHeight: heightPixel(510)
  },
  sheetContent: {
    alignItems: 'center',
  },

  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: heightPixel(58),
    borderWidth: 1,
    borderColor: 'rgba(34,31,32,.5)',
    borderRadius: widthPixel(8),
    paddingHorizontal: widthPixel(10),
    // marginBottom: widthPixel(20),
    width: '100%',
  },
  countryCode: {
    fontSize: fonts._14,
    fontFamily: fonts.FONT_FAMILY.Medium,
    color: '#221f20',
    marginRight: widthPixel(10),
  },
  phoneInput: {
    flex: 1,
    fontSize: fonts._14,
    color: '#000',
    fontFamily: fonts.FONT_FAMILY.Regular,
  },
  otpContainer: {
    width: '80%',
    height: heightPixel(70),
  },
  otpButton: {
    paddingVertical: widthPixel(12),
    borderRadius: widthPixel(8),
    alignItems: 'center',
    marginBottom: widthPixel(20),
    height: heightPixel(48),
    marginTop: widthPixel(20),
  },
  otpDisableButton: {
    // backgroundColor: '#ccc',
    paddingVertical: widthPixel(12),
    borderRadius: widthPixel(8),
    alignItems: 'center',
    marginBottom: widthPixel(20),
    marginTop: widthPixel(20),
    height: heightPixel(48),
  },
  otpButtonText: {
    color: '#fff',
    fontSize: fonts._16,
    fontFamily: fonts.FONT_FAMILY.Regular,
  },
  whatsappOptInContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: widthPixel(20),
    alignSelf: 'center',
    gap: widthPixel(10),
  },
  whatsappOptInText: {
    fontSize: fonts._12,
    fontFamily: fonts.FONT_FAMILY.Regular,
    color: '#221f20',
  },
  legalText: {
    fontSize: fonts._12,
    fontFamily: fonts.FONT_FAMILY.Regular,
    textAlign: 'center',
  },
  linkText: {
    color: '#428bc1',
    fontSize: fonts._14,
    fontFamily: fonts.FONT_FAMILY.Medium,
  },
  codeInputField: {
    fontSize: fonts._24,
    fontFamily: fonts.FONT_FAMILY.SemiBold,
    height: heightPixel(70),
    width: widthPixel(70),
    borderWidth: 1,
    borderColor: '#d7d7d7',
    borderRadius: widthPixel(10),
    marginHorizontal: widthPixel(5),
    color: 'black',
  },
  codeInputHighlight: {
    borderColor: '#000', // Highlight color when selected
  },
  resendButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: widthPixel(30),
    marginVertical: widthPixel(2),
  },
  resendButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: widthPixel(16),
    paddingVertical: widthPixel(10),
    backgroundColor: 'transparent',
    gap: widthPixel(8),
  },
  resendButtonText: {
    fontSize: fonts._14,
    fontFamily: fonts.FONT_FAMILY.Medium,
    color: '#428bc1',
  },
  resendButtonDisabled: {
    opacity: 0.5,
  },
  resendButtonTextDisabled: {
    color: '#ccc',
  },
  inlineCaptchaContainer: {
    marginBottom: widthPixel(15),
    width: '100%',
    alignItems: 'center',
  },
  // Captcha Styles
  captchaContainer: {
    flexDirection: 'row',
    gap: widthPixel(8),
    marginBottom: widthPixel(8),
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: widthPixel(20),
  },
  captchaBox: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: '#E5E5E5',
    borderRadius: widthPixel(8),
    padding: widthPixel(12),
    backgroundColor: '#f5f5f5',
    height: heightPixel(56),
    flex: 1,
  },
  captchaText: {
    fontSize: fonts._16,
    fontFamily: fonts.FONT_FAMILY.Bold,
    color: '#221f20',
    letterSpacing: 2,
    flex: 1,
    textAlign: 'center',
  },
  refreshButton: {
    padding: widthPixel(4),
  },
  captchaInputContainer: {
    flex: 1,
    flexDirection: 'column',
  },
  captchaInput: {
    borderWidth: 1,
    borderColor: '#E5E5E5',
    borderRadius: widthPixel(8),
    padding: widthPixel(12),
    fontSize: fonts._14,
    fontFamily: fonts.FONT_FAMILY.Regular,
    height: heightPixel(56),
    textAlign: 'center',
  },
  captchaMessage: {
    fontSize: fonts._12,
    fontFamily: fonts.FONT_FAMILY.Medium,
    textAlign: 'center',
    marginTop: heightPixel(4),
    paddingLeft: widthPixel(4),
  },
  captchaSuccess: {
    color: '#4CAF50',
  },
  captchaError: {
    color: '#F44336',
  },
});
