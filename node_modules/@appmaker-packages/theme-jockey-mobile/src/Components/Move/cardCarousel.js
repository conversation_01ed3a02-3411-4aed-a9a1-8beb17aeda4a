import { Dimensions, Image, StyleSheet, Text, View } from 'react-native'
import React, { useRef, useState } from 'react'
import ShopifyImage from '../ShopifyImage'
import { fontPixel, fonts, getVerticalPadding, heightPixel, widthPixel } from '../../styles'
import Carousel, { Pagination } from 'react-native-snap-carousel';

const { width } = Dimensions.get('window');

const cardCarousel = (props) => {
    const carouselRef = useRef(null);
    const [carouselKey, setCarouselKey] = useState(0);
    const [activeIndex, setActiveIndex] = useState(0);

    return (

        <View style={{ flex: 1, paddingHorizontal: widthPixel(6), marginTop: heightPixel(50), alignSelf: "center" }} >

            <ShopifyImage
                source={{
                    uri: props?.attributes?.image?.url
                }}
                style={{
                    height: heightPixel(450),
                    width: width * 0.95, borderRadius: widthPixel(9),
                    borderRadius: widthPixel(23)
                }}
                resizeMode={"cover"}
            />

            <View style={{
                position: "absolute",
                justifyContent: "center",
                alignItems: "center",
                alignSelf: "center",
                top: 10,
                fontFamily: "Jost-Bold"
            }} >
                <Text style={{
                    fontSize: fontPixel(27),
                    fontFamily: "sans-serif",
                    fontWeight: "700",
                    color: "#fff",
                    fontFamily: "Jost-Bold"
                }} >{props?.attributes?.Heading.toUpperCase()}</Text>

                <Text style={{
                    fontSize: fontPixel(27),
                    fontFamily: "sans-serif",
                    fontWeight: "700",
                    color: "#fff",
                }} >{props?.attributes?.Heading2.toUpperCase()}</Text>
            </View>

            <Carousel
                ref={carouselRef}
                key={carouselKey}
                layout={'default'}
                loop={false}
                // data={props?.innerBlocks.slice(0, 1)}
                data={props?.innerBlocks}
                sliderWidth={width * 0.95}
                itemWidth={widthPixel(200)}
                activeSlideAlignment="start"
                renderItem={({ item, index }) => {

                    return (

                        <View style={{
                            height: heightPixel(312),
                            width: widthPixel(165),
                            borderRadius: widthPixel(15),
                            justifyContent: "center",
                            alignItems: "center",
                            paddingHorizontal: widthPixel(5),
                            backgroundColor: "white",
                            alignSelf: "center",
                        }} >

                            <Image source={{ uri: item?.attributes?.image?.url }} style={{ height: heightPixel(150), width: widthPixel(150) }} />

                            {getVerticalPadding(25)}

                            <Text
                                style={{
                                    lineHeight: heightPixel(25),
                                    fontSize: fonts._12,
                                    fontFamily: "Jost-Variable,sans-serif",
                                    fontWeight: "600"
                                }} >{item?.attributes?.Title}</Text>
                            <Text
                                style={{
                                    textAlign: "center",
                                    fontSize: fonts._11,
                                    fontFamily: "Jost-Variable,sans-serif",
                                    fontWeight: "400"
                                }} >{item?.attributes?.Subtitle}</Text>

                        </View>

                    )
                }}
                containerCustomStyle={{
                    position: "absolute", bottom: 50,
                    paddingHorizontal: widthPixel(6),
                    alignSelf: "center",
                }}
                inactiveSlideOpacity={1}
                inactiveSlideScale={1}
                onSnapToItem={(index) => setActiveIndex(index)}
            />

            <View style={{
                flexDirection: "row",
                justifyContent: "center",
                alignItems: "center",
                marginTop: heightPixel(20),
                position: "absolute",
                alignSelf: "center",
                bottom: 25
            }}>
                {props?.innerBlocks?.map((item, index) => (
                    <View
                        key={index}
                        style={{
                            height: widthPixel(12),
                            width: widthPixel(12),
                            justifyContent: "center",
                            alignItems: "center",
                            borderWidth: index == activeIndex ? 2 : 0,
                            borderColor: "white",
                            padding: 5,
                            borderRadius: 100,
                            marginHorizontal: widthPixel(4),
                        }}
                    >
                        <View style={{
                            height: widthPixel(4),
                            width: widthPixel(4),
                            backgroundColor: index == activeIndex ? "white" : "#221f205e",
                            borderRadius: widthPixel(3),
                        }} />
                    </View>
                ))}
            </View>


        </View>

    )

}

export default cardCarousel

const styles = StyleSheet.create({})