import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  Image,
  ActivityIndicator,
} from 'react-native';
import React, { useEffect, useState } from 'react';
import { ScrollView } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Spinner from '../common/spinner';
import { Pressable } from 'react-native';
import { fonts, heightPixel, widthPixel } from '../../styles';
import { getSettings } from '../../../config';
import { useOrders, useCurrentUser, useUser } from '@appmaker-xyz/shopify';
import AsyncStorage from '@react-native-async-storage/async-storage';
export default function Edd({ variants, selectedVariant }) {
  const [pin, setPin] = useState();
  const [pinText, setPinText] = useState('');
  const [loading, setLoading] = useState(false);
  const [finalEdd, setFinalEdd] = useState('');
  const settings = getSettings();

  // Get current user and orders
  const { id: currentUserId } = useCurrentUser({}) || {};
  const { orderList, isLoading: ordersLoading } = useOrders({
    limit: 1
  });

  const extractProductVariantId = variants.map((value) => {
    return { variantId: value.node.id.split('/ProductVariant/')[1] };
  });

  // Function to save pincode to local storage
  const savePincodeToStorage = async (pincode) => {
    try {
      await AsyncStorage.setItem('lastUsedPincode', pincode);
      console.log('Pincode saved to storage:', pincode);
    } catch (error) {
      console.error('Error saving pincode to storage:', error);
    }
  };

  // Function to get pincode from local storage
  const getPincodeFromStorage = async () => {
    try {
      const pincode = await AsyncStorage.getItem('lastUsedPincode');
      return pincode;
    } catch (error) {
      console.error('Error getting pincode from storage:', error);
      return null;
    }
  };

  // Function to extract pincode with priority: Latest Order → Local Storage → Blank
  const prefillPincode = async () => {
    try {
      let pincodeToUse = null;
      let source = '';

      // PRIORITY 1: Check for pincode from latest order (only if user is logged in)
      if (currentUserId && !ordersLoading && orderList && orderList.length > 0) {
        const latestOrder = orderList[0]?.node;

        if (latestOrder?.shippingAddress?.zip) {
          pincodeToUse = latestOrder.shippingAddress.zip;
          source = 'latest order';
          console.log('Found pincode from latest order:', pincodeToUse);
        }
      }

      // PRIORITY 2: If no pincode from latest order, check local storage (works for both logged in and guest users)
      if (!pincodeToUse) {
        const storedPincode = await getPincodeFromStorage();
        if (storedPincode) {
          pincodeToUse = storedPincode;
          source = 'local storage';
          console.log('Using stored pincode from local storage:', pincodeToUse);
        }
      }

      // PRIORITY 3: If no pincode found anywhere, leave field blank (default behavior)
      if (!pincodeToUse) {
        console.log('No pincode found from latest order or local storage, field will remain blank');
        return;
      }

      // Only proceed if pincode is different from current pin or pin is empty
      if (pincodeToUse !== pin) {
        // Set the pincode in the input field
        setPin(pincodeToUse);

        // Save to local storage (update with latest pincode if from order)
        if (source === 'latest order') {
          await savePincodeToStorage(pincodeToUse);
        }

        // Show loading state and automatically trigger EDD check
        setLoading(true);
        fetchRequestEDD(pincodeToUse);
      }
    } catch (error) {
      console.error('Error prefilling pincode:', error);
      setLoading(false);
    }
  };

  // State to track if we've already attempted to prefill
  const [hasPrefilled, setHasPrefilled] = useState(false);

  useEffect(() => {
    setLoading(false);
    // Reset prefill state when component mounts (new product)
    setHasPrefilled(false);
  }, []);

  // Effect to prefill pincode when user and orders are loaded (only once)
  useEffect(() => {
    if (currentUserId && !ordersLoading && !hasPrefilled) {
      prefillPincode();
      setHasPrefilled(true);
    }
  }, [currentUserId, ordersLoading, hasPrefilled]);

  // Effect to prefill pincode for guest users (from local storage only)
  useEffect(() => {
    if (!currentUserId && !hasPrefilled) {
      prefillPincode();
      setHasPrefilled(true);
    }
  }, [currentUserId, hasPrefilled]);

  function handleDeliveryDetailbutton() {
    setLoading(true);

    if (!pin || pin.length !== 6) {
      setPinText('Please Enter a valid Pin');
      setLoading(false);
    } else {
      // Save pincode to storage when manually entered
      savePincodeToStorage(pin);
      fetchRequestEDD(pin);
      setPinText('');
    }
  }

  const fetchRequestEDD = async (pinData) => {
    const response = await fetch(
      settings.edd_api_url,
      {
        method: 'POST',
        headers: {
          accept: 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          dropPincode: pinData,
          quantity: 1,
          variantsData: extractProductVariantId,
        }),
      },
    );

    const responseData = await response.json();
    console.log(responseData, " response data")
    if (responseData.serviceable) {
      if (responseData?.variantsData?.length)
        setFinalEdd(responseData?.variantsData?.[0]?.edd);
      setPinText('');
      setLoading(false);
    } else {
      setLoading(false);
      setFinalEdd('');
      setPinText('Pincode not serviceable.');
    }
  };

  return (
    <View>
      <View
        style={{
          borderWidth: 0,
          elevation: 0,
          paddingVertical: heightPixel(8),
          borderRadius: widthPixel(10),
        }}>
        <Text
          style={{
            color: '#221f20',
            fontSize: fonts._16,
            fontFamily: fonts.FONT_FAMILY.SemiBold,
            marginBottom: heightPixel(10),
          }}>
          Delivery Details
        </Text>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            overflow: 'hidden',
            gap: widthPixel(8),
            flexWrap: 'wrap',
          }}>
          <View style={{ width: '60%' }}>
            <TextInput
              placeholder="Enter Your pincode"
              maxLength={6}
              keyboardType="numeric"
              onChangeText={(newPin) => {
                if (newPin === '') {
                  setPinText('');
                }
                const trimmedPin = newPin.trim();
                setPin(trimmedPin);

                // Save pincode to storage whenever user changes it
                if (trimmedPin.length > 0) {
                  savePincodeToStorage(trimmedPin);
                }
              }}
              value={pin}
              style={{
                borderWidth: 1,
                borderColor: 'rgba(34,31,32,.1)',
                padding: widthPixel(8),
                borderRadius: widthPixel(8),
                fontFamily: 'Jost-SemiBold',
                paddingLeft: widthPixel(20),
                height: heightPixel(50),
                alignItems: 'center', justifyContent: 'center',
              }}
            />
          </View>
          <View style={{ width: '32%' }}>
            <Pressable
              onPress={() => {
                handleDeliveryDetailbutton();
              }}>
              <LinearGradient
                colors={['#221f20', '#505050']}
                start={{ x: -0.3336, y: 0 }} // Adjusted based on the angle in the CSS
                end={{ x: 1.3952, y: 1 }} // Adjusted based on the angle in the CSS
                style={{
                  paddingHorizontal: widthPixel(10),
                  // paddingVertical: heightPixel(13),
                  height: heightPixel(50),
                  alignItems: 'center', justifyContent: 'center',
                  borderRadius: widthPixel(10),
                }}>
                {!loading ? (
                  <Text style={styles.buttonText}>CHECK</Text>
                ) : (
                  <View
                    style={{
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}>
                    {/* <Spinner /> */}
                    <ActivityIndicator size={'small'} color={'white'} />
                  </View>
                )}
              </LinearGradient>
            </Pressable>
          </View>
        </View>

        {pinText != '' && (
          <View style={{ paddingVertical: heightPixel(10) }}>
            <Text
              style={{ color: 'red', fontFamily: fonts.FONT_FAMILY.Regular }}>
              {pinText}
            </Text>
          </View>
        )}

        {/* EDD details , div needs to be visible when pincode entered  */}
        {finalEdd ? (
          <View>
            <ScrollView
              horizontal={true}
              style={{
                paddingVertical: heightPixel(10),
                borderWidth: 0,
                marginVertical: heightPixel(10),
                width: '100%',
              }}
              contentContainerStyle={{
                paddingRight: widthPixel(50),
              }}>
              <View style={{ display: 'flex', flexDirection: 'row' }}>
                <View
                  style={{
                    padding: widthPixel(10),
                    borderRadius: widthPixel(7),
                    borderWidth: 1,
                    borderColor: 'rgba(34,31,32,.1)',
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    width: '60%',
                    marginRight: widthPixel(10),
                    height: heightPixel(50),
                  }}>
                  <View
                    style={{ width: widthPixel(20), height: widthPixel(20) }}>
                    <Image
                      source={{
                        uri: 'https://www.jockey.in/cdn/shop/files/Group_239103.jpg?v=16732760107086212298',
                      }}
                      style={{
                        width: '100%',
                        height: '100%',
                        resizeMode: 'contain',
                      }}
                    />
                  </View>
                  <View>
                    <Text
                      style={{
                        fontSize: fonts._14,
                        fontFamily: fonts.FONT_FAMILY.Regular,
                      }}>
                      Estimated Delivery by{' '}
                      <Text
                        style={{
                          fontSize: fonts._13,
                          fontFamily: fonts.FONT_FAMILY.Bold,
                        }}>
                        {finalEdd}
                      </Text>
                    </Text>
                  </View>
                </View>

                {/* 3 */}
                <View
                  style={{
                    padding: widthPixel(10),
                    borderRadius: widthPixel(7),
                    borderWidth: 1,
                    borderColor: 'rgba(34,31,32,.1)',
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    width: '40%',
                  }}>
                  <View
                    style={{ width: widthPixel(20), height: widthPixel(20) }}>
                    <Image
                      source={{
                        uri: 'https://www.jockey.in/cdn/shop/files/Vector_1_a29858f8-e63b-4de4-9be6-3f78849099e3.png?v=9477741078544455416',
                      }}
                      style={{
                        width: '100%',
                        height: '100%',
                        resizeMode: 'contain',
                      }}
                    />
                  </View>
                  <Text
                    style={{
                      fontSize: fonts._13,
                      fontFamily: fonts.FONT_FAMILY.Regular,
                    }}>
                    {'Eligible for Free Delivery'}
                  </Text>
                </View>
              </View>
            </ScrollView>
          </View>
        ) : (
          <View></View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  buttonText: {
    fontSize: fonts._14,
    color: 'white',
    textAlign: 'center',
    fontFamily: fonts.FONT_FAMILY.Regular,
  },
});
