import { StyleSheet, Text, View } from 'react-native';
import React, { useEffect, useState } from 'react';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import Ripple from 'react-native-material-ripple';
import { handleAction } from '@appmaker-xyz/react-native';
import { useCart, useUser } from '@appmaker-xyz/shopify';
import { fonts, heightPixel, widthPixel } from '../../styles';
import LinearGradient from 'react-native-linear-gradient';
import { appStorageApi, useAppStorage, getUser } from '@appmaker-xyz/core';
import { getCheckoutUrl } from '../../utils/checkoutUtils';
import { Buffer } from 'buffer';

const CheckoutButton = (props) => {
  const { checkout } = useAppStorage();
  const { openCheckout, cartTotalPayableWithCurrency, cart, canCheckout, setCanCheckout } = useCart(props);
  const { isLoggedin } = useUser();

  const [redeemedAmountSum, setRedeemedAmountSum] = useState(0);
  const [loading, setLoading] = useState(true);
  const [redeemedGiftItems, setRedeemedGiftItems] = useState([]);

  // --- Fetch Redeemed Amount
  // const fetchRedeemedAmountSum = async () => {
  //   setLoading(true);
  //   try {
  //     const userData = getUser?.();
  //     const mobileNumber = userData?.phone || '';
  //     const encodedMobile = Buffer.from(mobileNumber).toString('base64');

  //     const response = await fetch('https://jockey-uat.myshopify.com/apps/uat-jr/api/route/redeemAmountSum', {
  //       method: 'POST',
  //       headers: {
  //         'Content-Type': 'application/json',
  //         'Origin': 'https://jockey-uat.myshopify.com/',
  //       },
  //       body: JSON.stringify({ mobilenumber: encodedMobile }),
  //     });

  //     const data = await response.json();
  //     console.log('redeemSumCheckout', data);

  //     const redeemAmount = Number(data.TotalRedeemAmount || 0);
  //     setRedeemedAmountSum(redeemAmount);

  //     const cartValue = Number(cart?.totalPrice?.amount || 0);
  //     const canProceed = redeemAmount < cartValue;

  //     setCanCheckout(canProceed);
  //   } catch (e) {
  //     console.warn('Redeem Amount API Error', e);
  //     setRedeemedAmountSum(0);
  //     setCanCheckout(true); // allow by default if API fails
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  // --- Effect to fetch on cart value change
  // useEffect(() => {
  //   fetchRedeemedAmountSum();
  // }, [cart?.totalPrice?.amount]);
  // const fetchRedeemedGiftItems = async () => {
  //   console.log("--redeemed gift items")
  //   try {
  //     const userData = getUser();
  //     const mobileNumber = userData?.phone || '';
  //     const encodedMobile = Buffer.from(mobileNumber).toString('base64');
  //     console.log("mobile", encodedMobile)
  //     const response = await fetch('https://jockey-uat.myshopify.com/apps/uat-jr/api/route/redeemDataList', {
  //       method: 'POST',
  //       headers: { 'Content-Type': 'application/json',  'Origin': "https://jockey-uat.myshopify.com/", },
  //       body: JSON.stringify({ mobilenumber: encodedMobile }),
  //     });
  //     const data = await response.json();
  //     console.log("redeemed gift items data", data)
  //     setRedeemedGiftItems(Array.isArray(data) ? data : []);
      
  //     // Wait for checkout ID to be available
  //     if (!checkout?.id) {
  //       console.log("Waiting for checkout ID...");
  //       return;
  //     }
      
  //     // Extract cart token
  //     const cartToken = (checkout?.id?.split('/').pop() || '').split('?')[0];
  //     console.log("Cart Token:", cartToken);
      
  //     // Process gift card data for cart attributes
  //     let providers = '';
  //     let gcNumbers = '';
  //     let gcAmounts = '';
  //     let shopify_gccodes = '';
      
  //     if (Array.isArray(data) && data.length > 0) {
  //       providers = data.map(card => card.provider || '').join(',');
  //       gcNumbers = data.map(card => card.number || '').join(',');
  //       gcAmounts = data.map(card => card.amount || '').join(',');
  //       shopify_gccodes = data.filter(card => card.gccode).map(card => card.gccode || '').join(',');
  //     }

  //     // Prepare cart attributes
  //     const cartAttributes = [
  //       { key: "cart_token", value: cartToken || "" },
  //       { key: "giftcard", value: Array.isArray(data) ? JSON.stringify(data) : "" },
  //       { key: "provider", value: providers || "" },
  //       { key: "gc_gv_number", value: gcNumbers || "" },
  //       { key: "gc_gv_amount", value: gcAmounts || "" },
  //       { key: "shopify_gc", value: shopify_gccodes || "" },
  //     ];

  //     console.log("Updating Cart Attributes:", JSON.stringify(cartAttributes, null, 2));

  //     // Update cart attributes
  //     if (props?.onAction) {
  //       props.onAction({
  //         action: 'UPDATE_CART_CUSTOM_ATTRIBUTES',
  //         params: {
  //           cartCustomAttributes: cartAttributes,
  //         },
  //       });

  //       // Log the cart attributes after a delay to verify the update
  //       setTimeout(() => {
  //         console.log("Current Cart Attributes:", cart?.customAttributes);
  //       }, 1000);
  //     } else {
  //       console.error("onAction prop is not available");
  //     }

  //     const validGCCodes = data.filter(item => item.gccode);
  //     // console.log("isRedeemAmountValid", isRedeemAmountValid)
  //     // Only apply gift cards if isRedeemAmountValid is true
  //     // if (isRedeemAmountValid) {
  //     //   if (validGCCodes.length > 1) {
  //     //     const codes = validGCCodes.map(item => item.gccode);
  //     //     console.log("Calling Appmaker action for multiple codes", codes);
  //     //     updateCartValueAll(codes)
  //     //   } else if (validGCCodes.length === 1) {
  //     //     console.log("Calling Appmaker action for single code", validGCCodes[0].gccode);
  //     //     updateCartValueOne(validGCCodes)
  //     //   }
  //     // } else {
  //     //   console.log('Total redeem amount exceeds cart value, not applying gift cards.');
  //     // }
  //   } catch (e) {
  //     console.error("Error in fetchRedeemedGiftItems:", e);
  //     setRedeemedGiftItems([]);
  //   }
  // };
  const handleCheckout = async () => {
    const currentCart = appStorageApi().getState().checkout;
    const checkoutUrl = getCheckoutUrl();

    // console.log('CHECKOUT URL:', checkoutUrl);
    // console.log('CHECKOUT PAYLOAD:', JSON.stringify(currentCart, null, 2));
    // await fetchRedeemedGiftItems();
    const shopifyCart = appStorageApi().getState().shopifyCart;
    if (shopifyCart) {
      // console.log('SHOPIFY CART:', JSON.stringify(shopifyCart, null, 2));
    }

    const user = appStorageApi().getState().user;
    if (user) {
      // console.log('USER INFO (for checkout headers):', {
      //   accessToken: user.accessToken ? 'Present (hidden)' : 'Not present',
      //   email: user.email,
      // });
    }

    await openCheckout();

    setTimeout(() => {
      const updatedCart = appStorageApi().getState().checkout;
      if (updatedCart !== currentCart) {
        console.log('CHECKOUT RESPONSE (UPDATED):', JSON.stringify(updatedCart, null, 2));
      }
    }, 1000);
  };

  return (
    <View style={styles.container}>
      <View style={styles.amountContainer}>
        <Text style={styles.amountText}>{cartTotalPayableWithCurrency}</Text>
        <Text style={styles.incl}>(Incl. Of All Taxes)</Text>
      </View>

      <LinearGradient
        start={{ x: 1, y: 1 }}
        end={{ x: 0, y: 0 }}
        colors={['#221f20', '#505050']}
        style={styles.button}>
        <Ripple
          // disabled={!canCheckout || loading}
          onPress={() => {
            if (isLoggedin) {
              handleCheckout();
            } else {
              handleAction({
                action: 'OPEN_INAPP_PAGE',
                pageId: 'LoginOptions',
                params: { isFromCart: true },
              });
            }
          }}
          style={{
            width: '100%',
            height: '100%',
            alignItems: 'center',
            justifyContent: 'center',
            // opacity: !canCheckout || loading ? 0.5 : 1,
          }}>
          <Text style={styles.buttonText}>PLACE ORDER</Text>
        </Ripple>
      </LinearGradient>
    </View>
  );
};

export default CheckoutButton;

const styles = StyleSheet.create({
  container: {
    paddingVertical: heightPixel(30),
    flexDirection: 'row',
    justifyContent: 'center',
    backgroundColor: '#fff',
    alignItems: 'center',
    borderTopWidth: 0.5,
    borderTopColor: '#cfcfcf',
  },
  amountContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  amountText: {
    fontSize: fonts._16,
    fontFamily: fonts.FONT_FAMILY.Medium,
    fontWeight: '600',
  },
  incl: {
    fontSize: fonts._11,
    fontFamily: fonts.FONT_FAMILY.Regular,
  },
  buttonText: {
    color: 'white',
    fontSize: fonts._16,
    fontFamily: fonts.FONT_FAMILY.Regular,
  },
  button: {
    flex: 1.5,
    justifyContent: 'center',
    alignItems: 'center',
    height: heightPixel(46),
    marginHorizontal: widthPixel(20),
    borderRadius: widthPixel(12),
  },
});
