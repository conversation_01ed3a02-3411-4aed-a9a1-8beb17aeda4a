
import CustomCategories from '../Components/HomePage/HomeCustomCategories';
import FeaturedCategories from '../Components/HomePage/HomeFeaturedCategories';
import OurStory from '../Components/HomePage/HomeOurStory';
import ProductCard from '../Components/HomePage/ProductCard';
import FeaturedSeller from '../Components/HomePage/HomeFeaturedSeller';
import BestSeller from '../Components/HomePage/HomeBestSeller';
import MenFeatured from '../Components/Men/MenFeatured';
import MoreColors from '../Components/Men/MoreColors';
import JournalCard from '../Components/Men/JournalCard';
import JournalCardWithShare from '../Components/Men/JournalCardWithShare';
import Footer from '../Components/Footer/Footer';
import FooterPopularSearches from '../Components/Footer/FooterPopularSearches';
import ProductImage from '../Components/PDP/ProductImage';
import ProductDetails from '../Components/PDP/ProductDetails';
import AddToCart from '../Components/PDP/AddToCart';
import OurCollection from '../Components/HomePage/HomeOurCollection';
import ProductFooter from '../Components/PDP/ProductFooter';
import CustomRelatedProducts from '../Components/customRelatedProducts';
import SizeChart from '../Components/PDP/SizeChart';
import SizeVarients from '../Components/PDP/SizeVarients';
import HomepageMainSlider from '../Components/HomePage/HomeHeroMainSlider';
import AccessoriesThatGrabYourStyle from '../Components/HomePage/HomeAccessoriesThatGrabYourStyle';
import MoreCollectionsToExplore from '../Components/HomePage/HomeMoreCollectionsToExplore';
import NewArrival from '../Components/HomePage/HomeNewArrival';
import ProductGridItem from '../Components/PLP/ProductGridItem';
import GameChangerForAthletes from '../Components/HomePage/HomeGameChangerForAthletes';
import Filter from '../Components/PLP/CollectionSortFilter/index';
import JockeyKids from '../Components/HomePage/HomeJockeyKids';
import JockeyJournal from '../Components/HomePage/HomeJockeyJournal';
import ColorsToExplore from '../Components/HomePage/HomeColorsToExplore';
import ProductListFilters from '../Components/PLP/ProductListFilters';
import CustomSizeFilter from '../Components/PLP/CustomSizeFilter';
import WomenCard from '../Components/PLP/WomenCard';
import OnTheMoveCard from '../Components/L1/Men/OnTheMoveCard';
import WomenL1 from '../Components/Men/WomenL1';
import KidsYearBanner from '../Components/Men/KidsYearBanner';
import SizeVarients1 from '../Components/PDP/ColorVariants';
import KnowYourJockey from '../Components/HomePage/HomeKnowYourJockey';
import Menu from '../Components/MenuDrawer/Menu';
import MetaFieldComp from '../Components/PDP/MetaFieldComp';
import BlogDetail from '../Components/Blog/BlogDetail';
import BlogPage from '../Components/Blog/BlogPage';
import GiftCard from '../Components/GiftCard/GiftCard';
import GiftCard1 from '../Components/GiftCard/TestGiftCard';
import TermsOfUse from '../Components/Static/TermsOfUse';
import ReturnPolicy from '../Components/Static/ReturnPolicy';
import PrivacyPolicy from '../Components/Static/PrivacyPolicy';
import AboutUs from '../Components/Static/AboutUs';
import OurBeliefs from '../Components/Static/OurBeliefs';
import WhyJoinUs from '../Components/Static/WhyJoinUs';
import GetInTouch from '../Components/Static/GetInTouch';
import HomeVictoryVibe from '../Components/HomePage/HomeVictoryVibe';
import QuickFilter from '../Components/PLP/QuickFilter';
import EverydayEssentials from '../Components/L1/Men/EverydayEssentials'
import EssentialsMadeAmazing from '../Components/L1/Men/EssentialsMadeAmazing';
import MediaSwiper from '../Components/L1/Men/HeroMediaSlider';
import DiscoverMoreCollections from '../Components/L1/Women/DiscoverMoreCollections';
import ComfortForActiveBoys from '../Components/L1/Kids/ComfortForActiveBoys';
import ShopByAge from '../Components/L1/Kids/ShopByAge';
import HomeFeaturedStyles from '../Components/HomePage/HomeFeaturedStyles';
import { SearchBlock } from '../Pages/SearchBlock';
import HomeBottomDesc from '../Components/HomePage/HomeBottomDesc';
import CartLineItemCard from '../Components/Cart/CartLineItemCard';
import CartSummaryTable from '../Components/Cart/CartSummaryTable';
import CheckoutButton from '../Components/Cart/CheckoutButton';
import ApplyCoupon from '../Components/Cart/ApplyCoupon';
import CustomCartBlock from '../Components/Cart/CustomCartBlock';
import SmartSizeFilter from '../Components/PLP/SmartSizeFilter';
import SmartPriceFilter from '../Components/PLP/SmartPriceFilter';
import { WishlistBlock } from '../Components/Account/WishListBlock';
import SearchList from '../Components/Search/SearchList';
import BuzzInTownPLP from '../Components/PLP/BuzzInTownPLP';
import BuzzInTownWomen from '../Components/L1/Women/BuzzInTownWomen';
import Login from '../Components/Account/Login';
import AccountInfo from '../Components/Account/AccountInfo';
import AccountMenu from '../Components/Account/AccountMenu';
import AccountLogout from '../Components/Account/AccountLogout';
import AccountOtherMenus from '../Components/Account/AccountOtherMenus';
import MediaSwiperWomen from '../Components/L1/Women/HeroMediaSliderWomen';
import PLPPageHead from '../Components/PLP/PLPPageHead';
import EmptyCart from '../Components/Cart/EmptyCart';
import { Platform, View } from 'react-native';
import { StickyHeader } from '../Components/HomePage/StickyHeader';
import StickySearch from '../Components/HomePage/StickySearch';
import { StickyHeaderiOS } from '../Components/HomePage/StickyHeaderiOS';
import ToolbarIcon from '../Components/common/ToolbarIcon';
import AccountDelete from '../Components/Account/AccountDelete';
import PlpEmpty from '../Components/PLP/PlpEmpty';
import CodedCasuals from '../Components/Groove/CodedCasuals';
import NewDrop from '../Components/Groove/NewDrop';
import TrendingNow from '../Components/Groove/TrendingNow';
import getGroovy from '../Components/Groove/getGroovy';
import PickAVibe from '../Components/Groove/PickAVibe';
import mensMove from '../Components/Move/mensMove';
import videoBgProductCarousel from '../Components/Move/videoBgProductCarousel';
import cardCarousel from '../Components/Move/cardCarousel';
import productCarousel from '../Components/Move/productCarousel';
import Move from '../Pages/Move';
import ForceUpdate from '../Components/common/ForceUpdate';


const blocks = [
  // homepage

  {
    name: 'appmaker/homepage-slider',
    View: HomepageMainSlider,
  },
  {
    name: 'appmaker/custom-categories',
    View: CustomCategories,
  },
  Platform.OS === 'ios' ?
    {
      name: 'custom/sticky-header',
      View: StickyHeader,
    } :
    {
      name: 'custom/sticky-header-android',
      View: StickyHeaderiOS,
    },
  {
    name: 'custom/sticky-header-for-search',
    View: StickySearch,
  },
  {
    name: 'appmaker/search-block',
    View: SearchBlock,
  },

  {
    name: 'appmaker/search-list',
    View: SearchList,
  },

  {
    name: 'appmaker/game-changer-for-athletes',
    View: GameChangerForAthletes,
  },
  {
    name: 'appmaker/new-arrival',
    View: NewArrival,
  },
  {
    name: 'appmaker/home-featured-styles',
    View: HomeFeaturedStyles,
  },
  {
    name: 'appmaker/bestseller',
    View: BestSeller,
  },
  {
    name: 'appmaker/colors-to-explore',
    View: ColorsToExplore,
  },
  {
    name: 'appmaker/custom-featured-categories',
    View: FeaturedCategories,
  },
  {
    name: 'appmaker/custom-home-victory-vibe',
    View: HomeVictoryVibe,
  },
  {
    name: 'appmaker/custom-our-story',
    View: OurStory,
  },
  {
    name: 'appmaker/our-collection',
    View: OurCollection,
  },
  {
    name: 'appmaker/more-collections-to-explore',
    View: MoreCollectionsToExplore,
  },
  {
    name: 'appmaker/jockey-kids',
    View: JockeyKids,
  },
  {
    name: 'appmaker/accessories-that-grab-your-style',
    View: AccessoriesThatGrabYourStyle,
  },

  {
    name: 'appmaker/jockey-journal',
    View: JockeyJournal,
  },
  {
    name: 'appmaker/journal-card',
    View: JournalCard,
  },
  {
    name: 'appmaker/knowyourjockey',
    View: KnowYourJockey,
  },
  {
    name: 'appmaker/home-bottom-desc',
    View: HomeBottomDesc,
  },
  // Groove
  {
    name: 'appmaker/coded-casuals',
    View: CodedCasuals
  },
  {
    name: 'appmaker/new-drop',
    View: NewDrop
  },
  {
    name: 'appmaker/trending-now',
    View: TrendingNow
  },
  {
    name: 'appmaker/get-groovy',
    View: getGroovy
  },
  {
    name: 'appmaker/custom-pick-a-vibe',
    View: PickAVibe
  },

  // Men's Move

  {
    name: 'appmaker/move-banners',
    View: mensMove
  },
  {
    name: 'appmaker/video-background-product-carousel',
    View: videoBgProductCarousel
  },
  {
    name: 'appmaker/card-carousel',
    View: cardCarousel
  },
  {
    name: 'appmaker/product-carousel',
    View: productCarousel
  },

  // In-App pages

  //plp-l1
  {
    name: 'appmaker/journal-card',
    View: JournalCardWithShare,
  },
  {
    name: 'appmaker/more-colors',
    View: MoreColors,
  },

  //PLP-L1-MEN  
  {
    name: 'appmaker/men-featured',
    View: MenFeatured,
  },
  {
    name: 'appmaker/prime-selection-menl1',
    View: OnTheMoveCard,
  },
  {
    name: 'appmaker/prime-selection-womenl1',
    View: OnTheMoveCard,
  },
  {
    name: 'appmaker/women-move',
    View: WomenL1,
  },
  {
    name: 'appmaker/buzz-in-town',
    View: BuzzInTownWomen,
  },
  {
    name: 'appmaker/kids-year-banner',
    View: KidsYearBanner,
  },
  {
    name: 'appmaker/everyday-essentials',
    View: EverydayEssentials
  },
  {
    name: 'appmaker/essentials-made-amazing',
    View: EssentialsMadeAmazing,
  },

  {
    name: 'appmaker/hero-image-video-slider-menl1',
    View: MediaSwiper,
  },
  {
    name: 'appmaker/hero-image-video-slider-womenl1',
    View: MediaSwiperWomen,
  },
  {
    name: 'appmaker/hero-image-video-slider-kidsl1',
    View: MediaSwiper,
  },

  {
    name: 'appmaker/discover-more-collections-womenl1',
    View: DiscoverMoreCollections,
  },
  {
    name: 'appmaker/comfort-for-boys-kidsl1',
    View: ComfortForActiveBoys,
  },
  {
    name: 'appmaker/shop-by-age-kidsl1',
    View: ShopByAge,
  },

  // Navigation
  {
    name: "appmaker/core-drawer-menu",
    View: Menu,
  },
  // {
  //   name: "appmaker/drawer-menu-header",
  //   View: NavigationMenu,
  // },

  // footer
  {
    name: 'appmaker/footer',
    View: Footer,
  },

  {
    name: 'appmaker/popular-searches',
    View: FooterPopularSearches,
  },

  //PDP
  {
    name: 'appmaker/shopify-product-image',
    View: ProductImage,
  },
  {
    name: 'appmaker/shopify-product-data',
    View: ProductDetails,
  },
  {
    name: 'appmaker/shopify-product-variation',
    View: SizeVarients,
  },
  {
    name: 'appmaker/custom-size-variation',
    View: SizeVarients1,
  },
  // {
  //   name: 'appmaker/custom-pdp-buttons',
  //   View: AddToCart,
  // },
  {
    name: 'appmaker/size-chart',
    View: SizeChart,
  },
  {
    name: 'appmaker/custom-pdp-product',
    View: ProductFooter,
  },
  {
    name: 'my-theme/custom-related-products',
    View: CustomRelatedProducts,
  },
  {
    name: 'appmaker/meta-fields-value',
    View: MetaFieldComp,
  },

  //PLP
  {
    name: 'appmaker/product-card',
    View: ProductCard,
  },
  // {
  //   name: 'appmaker/modified-product-card',
  //   View: ProductCard1,
  // },
  {
    name: 'appmaker/product-grid-item',
    View: ProductGridItem,
  },
  {
    name: 'shopify/collection-filter',
    View: Filter,
  },
  {
    name: 'appmaker/product-list-filter',
    View: ProductListFilters,
  },

  {
    name: 'custom/quick-filter-plp',
    View: QuickFilter,
  },
  {
    name: 'appmaker/plp-size-filter',
    View: CustomSizeFilter,
  },
  {
    name: 'custom/plp-buzz-in-town',
    View: BuzzInTownPLP,
  },
  {
    name: 'appmaker/women-card',
    View: WomenCard,
  },
  {
    name: 'custom/plp-smart-size-filter',
    View: SmartSizeFilter,
  },
  {
    name: 'custom/plp-smart-price-filter',
    View: SmartPriceFilter,
  },
  {
    name: 'custom/plp-page-head',
    View: PLPPageHead,
  },
  {
    name: 'appmaker/error-block',
    View: PlpEmpty,
  },
  {
    name: 'appmaker/custom-move',
    View: Move,
  },

  // BLog
  {
    name: 'appmaker/blog-page',
    View: BlogPage,
  },
  {
    name: 'appmaker/blog-detail',
    View: BlogDetail,
  },

  // cart 
  {
    name: 'shopify/cart-line-item',
    View: CartLineItemCard,
  },

  {
    name: 'appmaker/custom-cart-block',
    View: CustomCartBlock,
  },

  {
    name: 'shopify/cart-summary-table',
    View: CartSummaryTable,
  },
  {
    name: 'appmaker/checkout-button',
    View: CheckoutButton,
  },
  {
    name: 'shopify/apply-coupon',
    View: ApplyCoupon,
  },


  {
    name: 'appmaker/gift-card',
    View: GiftCard1,
  },
  {
    name: 'appmaker/empty-cart-block',
    View: EmptyCart,
  },



  //static pages
  {
    name: 'appmaker/termsofuse',
    View: TermsOfUse,
  },
  {
    name: 'appmaker/returnPolicy',
    View: ReturnPolicy,
  },
  {
    name: 'appmaker/privacyPolicy',
    View: PrivacyPolicy,
  },
  {
    name: 'appmaker/aboutUs',
    View: AboutUs,
  },
  {
    name: 'appmaker/ourBeliefs',
    View: OurBeliefs,
  },
  {
    name: 'appmaker/whyJoinUs',
    View: WhyJoinUs,
  },
  {
    name: 'appmaker/getintouch',
    View: GetInTouch,
  },

  //Wishlist
  {
    name: 'appmaker/shopify-wishlist',
    View: WishlistBlock,
  },

  //login 
  {
    name: 'appmaker/login-block',
    View: Login,
  },

  // Account 
  {
    name: 'appmaker/account-info',
    View: AccountInfo,
  },
  {
    name: 'appmaker/account-menu',
    View: AccountMenu,
  },
  // {
  //   name: 'appmaker/account-delete',
  //   View: AccountDelete,
  // },
  {
    name: 'appmaker/account-logout',
    View: AccountLogout,
  },
  {
    name: 'appmaker/account-other-menus',
    View: AccountOtherMenus,
  },

  //Toolbar
  {
    name: 'custom/toolbar-icon',
    View: ToolbarIcon
  },
  // Force update
  {
    name: 'custom/ForceUpdateScreen',
    View: ForceUpdate
  }
];

export { blocks };
