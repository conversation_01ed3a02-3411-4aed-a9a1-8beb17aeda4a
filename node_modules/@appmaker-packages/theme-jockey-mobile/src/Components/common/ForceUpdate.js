import { Dimensions, Image, Pressable, StyleSheet, Text, View } from 'react-native'
import React from 'react'
import LinearGradient from 'react-native-linear-gradient';
import { fonts, getVerticalPadding, heightPixel, widthPixel } from '../../styles';

const screenWidth = Dimensions.get('window').width;
const screenHeight = Dimensions.get('window').height;

const ForceUpdate = (props) => {

    return (

        <View style={{ height: screenHeight, width: screenWidth, justifyContent: "center", alignItems: "center", backgroundColor: "white" }} >

            <Image
                style={{ height: 105, width: 105 }}
                source={require("../../assets/images/ForceUpdateLogo.png")}
            />

            {getVerticalPadding(27)}

            <View style={{ width: widthPixel(257), alignItems: "center" }} >
                <Text style={{ fontFamily: "Jost-Bold", fontSize: fonts._20, }} >Update Available</Text>

                {getVerticalPadding(17)}

                <Text style={{ fontFamily: "Jost-Regular", fontSize: fonts._14, textAlign: "center" }} >Please install the latest version to continue using the app without interruptions.</Text>
            </View>


            {getVerticalPadding(36)}

            <LinearGradient
                colors={['#221f20', '#505050']}
                start={{ x: 1, y: 1 }}
                end={{ x: 0, y: 0 }}
                style={{
                    width: '100%',
                    height: heightPixel(47),
                    width: widthPixel(211),
                    backgroundColor: "black",
                    borderRadius: widthPixel(10)
                }}
            >
                <Pressable onPress={() => onAction({
                    action: 'OPEN_APP_STORE_PLAYSTORE',
                })} style={{ height: "100%", width: "100%", justifyContent: "center", alignItems: "center" }} >

                    <Text style={{ color: "white", fontSize: fonts._16, fontWeight: "400", fontFamily: "Jost-Regular" }} >UPDATE APP</Text>

                </Pressable>

            </LinearGradient>


        </View >

    )
}

export default ForceUpdate

const styles = StyleSheet.create({})