import {
  ScrollView,
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ImageBackground,
  Dimensions,
} from 'react-native';
import React, { useCallback, useState } from 'react';
import Icon from 'react-native-vector-icons/AntDesign';
import Svg, { Path, Rect } from 'react-native-svg';
import { currencyHelper } from '@appmaker-xyz/shopify';
import { Pressable } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
const { height, width } = Dimensions.get('window');
import { useProductWishList, useUser } from '@appmaker-xyz/shopify';
import { fonts, heightPixel, widthPixel } from '../../styles';
import { getIdFromGID, updateWebWishlist } from '../../utils/Helper';

const variantSelectionPosition = (screenWidth) => {
  if (screenWidth > 400) {
    return 19;
  } else if (screenWidth > 250) {
    return 17;
  } else {
    return 15;
  }
};

export default function PrimeSelectionProductCard({
  title,
  productNode,
  productVariantDetails,
  props,
  onClick,
  showHalfOfAnother,
  openModal,
  firstImage = false
}) {
  const updatedProps = {
    ...props,
    blockData: {
      node: productNode,
    },
  };

  const { toggleWishList, isSaved } = useProductWishList(updatedProps);

  const { user } = useUser();

  const customerId = getIdFromGID(user?.id)

  const updateWishlist = () => {
    toggleWishList();
    if (!customerId) return
    const productId = getIdFromGID(productNode?.id);
    const variantId = getIdFromGID(productNode?.variants?.edges[0]?.node?.id);
    const action = isSaved ? "remove" : "add";
    updateWebWishlist(customerId, productId, variantId, action)
  }

  const openVariantModal = () => {
    let selectedProductData = {
      productNode,
      productVariantDetails,
    };
    openModal(selectedProductData);
  };

  const moveProductToFirst = useCallback((response, productIdToMoveFirst) => {
    let productToMove;
    const filteredNodes = [];

    if (response.nodes) {
      response.nodes.forEach((node) => {
        if (node.id === productIdToMoveFirst) {
          productToMove = node;
        } else {
          filteredNodes.push(node);
        }
      });
    }

    if (response.length > 0) {
      response?.variants?.edges?.forEach((node) => {
        if (node.id === productIdToMoveFirst) {
          productToMove = node;
        } else {
          filteredNodes.push(node);
        }
      });
    }

    if (productToMove) {
      return {
        ...response,
        nodes: [productToMove, ...filteredNodes],
      };
    }

    return response;
  }, []);

  const styleNumber = productNode?.style_number?.value;

  return (
    // <View>
    <View
      style={[
        showHalfOfAnother ? styles.cardContainerHalf : styles.cardContainer,
      ]}>
      <View style={styles.imageContainer}>
        <Pressable onPress={onClick}>
          <ImageBackground
            source={{ uri: !firstImage ? productNode?.images?.edges?.[4]?.node?.url : productNode?.images?.edges?.[0]?.node?.url }}
            style={styles.image}>
            {/* Best seller icon login here */}
            {productNode?.tags?.includes('Bestseller') && (
              <Text style={styles.bestSellerIcon}>
                <Svg
                  width="32"
                  height="71"
                  viewBox="0 0 32 71"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <Path
                    d="M29.5742 0.394418C29.526 0.391573 29.4764 0.390625 29.4252 0.390625C29.3763 0.390625 29.3281 0.391573 29.2822 0.393469H2.86625C2.8203 0.391573 2.77286 0.390625 2.7232 0.390625C2.67206 0.390625 2.6224 0.392521 2.57422 0.394418C2.77434 0.892196 2.91369 1.31223 3.0019 1.59382C3.64082 3.63425 3.77868 5.37885 3.77646 6.21132C3.77572 6.56877 3.77498 6.92622 3.7735 7.28272H3.77646V70.3906C5.16771 68.6109 16.0738 56.3732 16.0738 56.3732C16.0738 56.3732 26.98 68.6109 28.3712 70.3906V7.28368H28.3742C28.3734 6.92623 28.3727 6.56972 28.3712 6.21227C28.369 5.37979 28.5069 3.6352 29.1458 1.59477C29.234 1.31317 29.3733 0.893144 29.5735 0.395366L29.5742 0.394418Z"
                    fill="#3D3B3B"></Path>
                  <Path
                    d="M16.7728 9.86062L17.2428 9.86062L17.2428 10.9906C17.2428 11.224 17.2828 11.434 17.3628 11.6206C17.4428 11.8073 17.5594 11.954 17.7128 12.0606C17.8661 12.1673 18.0528 12.2206 18.2728 12.2206C18.6061 12.2206 18.8628 12.104 19.0428 11.8706C19.2294 11.6373 19.3228 11.344 19.3228 10.9906L19.3228 10.0406L13.8228 10.0406L13.8228 11.0906C13.8228 11.3906 13.8761 11.654 13.9828 11.8806C14.0894 12.114 14.2428 12.294 14.4428 12.4206C14.6428 12.554 14.8861 12.6206 15.1728 12.6206C15.3861 12.6206 15.5761 12.584 15.7428 12.5106C15.9094 12.4373 16.0494 12.3306 16.1628 12.1906C16.2761 12.0573 16.3594 11.8973 16.4128 11.7106C16.4728 11.524 16.5028 11.3173 16.5028 11.0906L16.5028 9.86062L16.9728 9.86062L16.9728 11.0906C16.9728 11.4306 16.9361 11.744 16.8628 12.0306C16.7894 12.3173 16.6761 12.5673 16.5228 12.7806C16.3761 13.0006 16.1861 13.1706 15.9528 13.2906C15.7194 13.4106 15.4428 13.4706 15.1228 13.4706C14.6628 13.4706 14.2794 13.364 13.9728 13.1506C13.6728 12.944 13.4461 12.6606 13.2928 12.3006C13.1461 11.9473 13.0728 11.544 13.0728 11.0906L13.0728 9.19062L20.0728 9.19063L20.0728 10.9906C20.0728 11.4106 20.0094 11.774 19.8828 12.0806C19.7561 12.394 19.5628 12.6373 19.3028 12.8106C19.0494 12.984 18.7228 13.0706 18.3228 13.0706C17.9961 13.0706 17.7161 12.984 17.4828 12.8106C17.2494 12.6373 17.0728 12.394 16.9528 12.0806C16.8328 11.774 16.7728 11.4106 16.7728 10.9906L16.7728 9.86062ZM12.9728 16.4968C12.9728 16.0701 13.0728 15.6901 13.2728 15.3568C13.4794 15.0301 13.7628 14.7735 14.1228 14.5868C14.4828 14.4068 14.8994 14.3168 15.3728 14.3168C15.8528 14.3168 16.2728 14.4101 16.6328 14.5968C16.9928 14.7901 17.2728 15.0568 17.4728 15.3968C17.6728 15.7435 17.7728 16.1435 17.7728 16.5968C17.7728 17.0501 17.6761 17.4368 17.4828 17.7568C17.2961 18.0835 17.0261 18.3335 16.6728 18.5068C16.3194 18.6801 15.8994 18.7668 15.4128 18.7668C15.3661 18.7668 15.3161 18.7635 15.2628 18.7568C15.2161 18.7568 15.1861 18.7568 15.1728 18.7568L15.1728 14.8268L15.8528 14.8268L15.8528 18.1268L15.3928 17.8068C15.4261 17.8268 15.4761 17.8468 15.5428 17.8668C15.6161 17.8868 15.6761 17.8968 15.7228 17.8968C15.9961 17.8968 16.2328 17.8401 16.4328 17.7268C16.6328 17.6201 16.7894 17.4701 16.9028 17.2768C17.0161 17.0835 17.0728 16.8568 17.0728 16.5968C17.0728 16.2901 17.0061 16.0268 16.8728 15.8068C16.7461 15.5935 16.5594 15.4268 16.3128 15.3068C16.0728 15.1935 15.7761 15.1335 15.4228 15.1268C15.0628 15.1268 14.7561 15.1835 14.5028 15.2968C14.2494 15.4101 14.0561 15.5768 13.9228 15.7968C13.7894 16.0168 13.7228 16.2835 13.7228 16.5968C13.7228 16.9101 13.7894 17.1868 13.9228 17.4268C14.0628 17.6668 14.2728 17.8701 14.5528 18.0368L14.1428 18.6868C13.7561 18.4468 13.4628 18.1435 13.2628 17.7768C13.0694 17.4168 12.9728 16.9901 12.9728 16.4968ZM14.4828 20.0533C14.3428 20.1466 14.2128 20.2533 14.0928 20.3733C13.9728 20.4933 13.8761 20.6299 13.8028 20.7833C13.7361 20.9433 13.7028 21.1166 13.7028 21.3033C13.7028 21.5299 13.7528 21.7066 13.8528 21.8333C13.9594 21.9666 14.1028 22.0333 14.2828 22.0333C14.4428 22.0333 14.5761 21.9799 14.6828 21.8733C14.7961 21.7733 14.8894 21.6433 14.9628 21.4833C15.0428 21.3233 15.1161 21.1533 15.1828 20.9733C15.2628 20.7733 15.3561 20.5699 15.4628 20.3633C15.5761 20.1633 15.7194 19.9933 15.8928 19.8533C16.0728 19.7199 16.2961 19.6533 16.5628 19.6533C16.8361 19.6533 17.0628 19.7233 17.2428 19.8633C17.4228 20.0099 17.5561 20.1966 17.6428 20.4233C17.7294 20.6566 17.7728 20.8966 17.7728 21.1433C17.7728 21.3899 17.7328 21.6199 17.6528 21.8333C17.5728 22.0533 17.4661 22.2466 17.3328 22.4133C17.1994 22.5799 17.0461 22.7099 16.8728 22.8033L16.4628 22.1633C16.6361 22.0366 16.7794 21.8766 16.8928 21.6833C17.0061 21.4966 17.0628 21.2833 17.0628 21.0433C17.0628 20.8766 17.0261 20.7333 16.9528 20.6133C16.8794 20.4933 16.7661 20.4333 16.6128 20.4333C16.4928 20.4333 16.3861 20.4799 16.2928 20.5733C16.1994 20.6666 16.1161 20.7866 16.0428 20.9333C15.9694 21.0799 15.8994 21.2333 15.8328 21.3933C15.7261 21.6599 15.6094 21.9033 15.4828 22.1233C15.3628 22.3433 15.2161 22.5166 15.0428 22.6433C14.8694 22.7766 14.6461 22.8433 14.3728 22.8433C13.9728 22.8433 13.6394 22.6966 13.3728 22.4033C13.1061 22.1166 12.9728 21.7366 12.9728 21.2633C12.9728 20.9566 13.0261 20.6799 13.1328 20.4333C13.2461 20.1866 13.3894 19.9766 13.5628 19.8033C13.7361 19.6366 13.9128 19.5099 14.0928 19.4233L14.4828 20.0533ZM17.6728 23.1965L17.6728 25.4965L16.9228 25.4965L16.9228 23.1965L17.6728 23.1965ZM19.2728 23.9465L19.2728 24.7465L13.0728 24.7465L13.0728 23.9465L19.2728 23.9465ZM14.4828 26.4888C14.3428 26.5822 14.2128 26.6888 14.0928 26.8088C13.9728 26.9288 13.8761 27.0655 13.8028 27.2188C13.7361 27.3788 13.7028 27.5522 13.7028 27.7388C13.7028 27.9655 13.7528 28.1422 13.8528 28.2688C13.9594 28.4022 14.1028 28.4688 14.2828 28.4688C14.4428 28.4688 14.5761 28.4155 14.6828 28.3088C14.7961 28.2088 14.8894 28.0788 14.9628 27.9188C15.0428 27.7588 15.1161 27.5888 15.1828 27.4088C15.2628 27.2088 15.3561 27.0055 15.4628 26.7988C15.5761 26.5988 15.7194 26.4288 15.8928 26.2888C16.0728 26.1555 16.2961 26.0888 16.5628 26.0888C16.8361 26.0888 17.0628 26.1588 17.2428 26.2988C17.4228 26.4455 17.5561 26.6322 17.6428 26.8588C17.7294 27.0922 17.7728 27.3322 17.7728 27.5788C17.7728 27.8255 17.7328 28.0555 17.6528 28.2688C17.5728 28.4888 17.4661 28.6822 17.3328 28.8488C17.1994 29.0155 17.0461 29.1455 16.8728 29.2388L16.4628 28.5988C16.6361 28.4722 16.7794 28.3122 16.8928 28.1188C17.0061 27.9322 17.0628 27.7188 17.0628 27.4788C17.0628 27.3122 17.0261 27.1688 16.9528 27.0488C16.8794 26.9288 16.7661 26.8688 16.6128 26.8688C16.4928 26.8688 16.3861 26.9155 16.2928 27.0088C16.1994 27.1022 16.1161 27.2222 16.0428 27.3688C15.9694 27.5155 15.8994 27.6688 15.8328 27.8288C15.7261 28.0955 15.6094 28.3388 15.4828 28.5588C15.3628 28.7788 15.2161 28.9522 15.0428 29.0788C14.8694 29.2122 14.6461 29.2788 14.3728 29.2788C13.9728 29.2788 13.6394 29.1322 13.3728 28.8388C13.1061 28.5522 12.9728 28.1722 12.9728 27.6988C12.9728 27.3922 13.0261 27.1155 13.1328 26.8688C13.2461 26.6222 13.3894 26.4122 13.5628 26.2388C13.7361 26.0722 13.9128 25.9455 14.0928 25.8588L14.4828 26.4888ZM12.9728 32.112C12.9728 31.6854 13.0728 31.3054 13.2728 30.972C13.4794 30.6454 13.7628 30.3887 14.1228 30.202C14.4828 30.022 14.8994 29.932 15.3728 29.932C15.8528 29.932 16.2728 30.0254 16.6328 30.212C16.9928 30.4054 17.2728 30.672 17.4728 31.012C17.6728 31.3587 17.7728 31.7587 17.7728 32.212C17.7728 32.6654 17.6761 33.052 17.4828 33.372C17.2961 33.6987 17.0261 33.9487 16.6728 34.122C16.3194 34.2954 15.8994 34.382 15.4128 34.382C15.3661 34.382 15.3161 34.3787 15.2628 34.372C15.2161 34.372 15.1861 34.372 15.1728 34.372L15.1728 30.442L15.8528 30.442L15.8528 33.742L15.3928 33.422C15.4261 33.442 15.4761 33.462 15.5428 33.482C15.6161 33.502 15.6761 33.512 15.7228 33.512C15.9961 33.512 16.2328 33.4554 16.4328 33.342C16.6328 33.2354 16.7894 33.0854 16.9028 32.892C17.0161 32.6987 17.0728 32.472 17.0728 32.212C17.0728 31.9054 17.0061 31.642 16.8728 31.422C16.7461 31.2087 16.5594 31.042 16.3128 30.922C16.0728 30.8087 15.7761 30.7487 15.4228 30.742C15.0628 30.742 14.7561 30.7987 14.5028 30.912C14.2494 31.0254 14.0561 31.192 13.9228 31.412C13.7894 31.632 13.7228 31.8987 13.7228 32.212C13.7228 32.5254 13.7894 32.802 13.9228 33.042C14.0628 33.282 14.2728 33.4854 14.5528 33.652L14.1428 34.302C13.7561 34.062 13.4628 33.7587 13.2628 33.392C13.0694 33.032 12.9728 32.6054 12.9728 32.112ZM20.8728 35.4785L20.8728 36.2785L13.0728 36.2785L13.0728 35.4785L20.8728 35.4785ZM20.8728 37.7832L20.8728 38.5832L13.0728 38.5832L13.0728 37.7832L20.8728 37.7832ZM12.9728 41.8679C12.9728 41.4412 13.0728 41.0612 13.2728 40.7279C13.4794 40.4012 13.7628 40.1446 14.1228 39.9579C14.4828 39.7779 14.8994 39.6879 15.3728 39.6879C15.8528 39.6879 16.2728 39.7812 16.6328 39.9679C16.9928 40.1612 17.2728 40.4279 17.4728 40.7679C17.6728 41.1146 17.7728 41.5146 17.7728 41.9679C17.7728 42.4212 17.6761 42.8079 17.4828 43.1279C17.2961 43.4546 17.0261 43.7046 16.6728 43.8779C16.3194 44.0512 15.8994 44.1379 15.4128 44.1379C15.3661 44.1379 15.3161 44.1346 15.2628 44.1279C15.2161 44.1279 15.1861 44.1279 15.1728 44.1279L15.1728 40.1979L15.8528 40.1979L15.8528 43.4979L15.3928 43.1779C15.4261 43.1979 15.4761 43.2179 15.5428 43.2379C15.6161 43.2579 15.6761 43.2679 15.7228 43.2679C15.9961 43.2679 16.2328 43.2112 16.4328 43.0979C16.6328 42.9912 16.7894 42.8412 16.9028 42.6479C17.0161 42.4546 17.0728 42.2279 17.0728 41.9679C17.0728 41.6612 17.0061 41.3979 16.8728 41.1779C16.7461 40.9646 16.5594 40.7979 16.3128 40.6779C16.0728 40.5646 15.7761 40.5046 15.4228 40.4979C15.0628 40.4979 14.7561 40.5546 14.5028 40.6679C14.2494 40.7812 14.0561 40.9479 13.9228 41.1679C13.7894 41.3879 13.7228 41.6546 13.7228 41.9679C13.7228 42.2812 13.7894 42.5579 13.9228 42.7979C14.0628 43.0379 14.2728 43.2412 14.5528 43.4079L14.1428 44.0579C13.7561 43.8179 13.4628 43.5146 13.2628 43.1479C13.0694 42.7879 12.9728 42.3612 12.9728 41.8679ZM17.6728 46.0344L13.0728 46.0344L13.0728 45.2344L17.6728 45.2344L17.6728 46.0344ZM16.7928 47.4244C16.8728 47.3177 16.9294 47.221 16.9628 47.1344C17.0028 47.0477 17.0228 46.9377 17.0228 46.8044C17.0228 46.6044 16.9728 46.4477 16.8728 46.3344C16.7728 46.221 16.6361 46.141 16.4628 46.0944C16.2894 46.0544 16.0928 46.0344 15.8728 46.0344L15.8728 45.6744C16.2394 45.6744 16.5628 45.7377 16.8428 45.8644C17.1294 45.9977 17.3561 46.1677 17.5228 46.3744C17.6894 46.581 17.7728 46.791 17.7728 47.0044C17.7728 47.171 17.7494 47.3244 17.7028 47.4644C17.6628 47.611 17.5794 47.7444 17.4528 47.8644L16.7928 47.4244Z"
                    fill="white"></Path>
                </Svg>
              </Text>
            )}
          </ImageBackground>
        </Pressable>

        <Pressable
          style={[
            styles.wishlistIcon,
            {
              backgroundColor: isSaved ? 'rgba(0, 0, 0, 0.11)' : 'transparent',
            },
          ]}
          onPress={updateWishlist}>
          {/* Add your wishlist icon here */}
          <Icon
            style={{ marginTop: 2 }}
            name={isSaved ? 'heart' : 'hearto'}
            size={widthPixel(17)}
            color={isSaved ? 'white' : 'white'}
          />
        </Pressable>

        <Pressable
          style={styles.variantSelector}
          onPress={openVariantModal}
          activeOpacity={0.7}>
          <Text style={styles.variantSelectorText}>
            {(productNode?.color_variant?.value &&
              JSON.parse(productNode?.color_variant?.value)?.length) ||
              (productNode?.metafield?.value &&
                productNode?.metafield?.value.length) ||
              '?'}
          </Text>
          <Svg
            xmlns="http://www.w3.org/2000/svg"
            width="28"
            height="20"
            viewBox="0 0 48 37"
            fill="none">
            <Rect
              x="28.98"
              y="5.774"
              width="18.281"
              height="25.313"
              rx="3.516"
              fill="#C46B4F"
              stroke="#fff"
              strokeWidth="1.406"
            />
            <Rect
              x="0.855"
              y="5.774"
              width="16.875"
              height="25.313"
              rx="3.516"
              fill="#557CA6"
              stroke="#fff"
              strokeWidth="1.406"
            />
            <Rect
              x="10.699"
              y="1.556"
              width="26.719"
              height="33.75"
              rx="6.328"
              fill="#464F51"
              stroke="#fff"
              strokeWidth="1.406"
            />
          </Svg>
        </Pressable>
      </View>

      <View style={styles.tagContainer}>
        <Text style={styles.variantsTag}>
          {productNode?.style_number?.value ||
            productNode?.metafields?.[0].value ||
            'n/a'}
        </Text>
      </View>

      <Text style={styles.productTitle} numberOfLines={2}>
        {title}
      </Text>

      <View style={styles.bottomContainer}>
        <Text style={styles.productPrice}>
          {currencyHelper(
            productNode?.priceRange?.minVariantPrice?.amount,
            productNode?.priceRange?.minVariantPrice?.currencyCode,
          )}
        </Text>
        <LinearGradient
          start={{ x: 1, y: 1 }} // Top-left corner
          end={{ x: 0, y: 0 }}
          colors={['#221f20', '#505050']}
          style={{ borderTopLeftRadius: 10, borderBottomRightRadius: 10 }}>
          <Pressable
            style={styles.cartIcon}
            activeOpacity={0.9}
            onPress={openVariantModal}>
            <View
              style={{
                flex: 1,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <Svg
                width="18"
                height="19"
                viewBox="0 0 18 19"
                fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <Path
                  fill-rule="evenodd"
                  clip-rule="evenodd"
                  d="M11.2988 3.93606C11.2988 1.90046 9.60755 0.231445 7.5405 0.231445C5.47345 0.231445 3.7823 1.90053 3.7823 3.93593V5.46251H0.859289C0.504293 5.46251 0.23291 5.72707 0.23291 6.07314V15.6398C0.23291 17.3293 1.63178 18.693 3.3648 18.693H8.7463C8.52841 18.3161 8.34935 17.9139 8.21458 17.4919L3.36452 17.492C2.3205 17.492 1.48538 16.6778 1.48538 15.6601V6.70401L3.7823 6.70387V7.76225C3.7823 8.10833 4.05368 8.37289 4.40867 8.37289C4.76367 8.37289 5.03505 8.10833 5.03505 7.76225V6.70387H10.0461V7.76225C10.0461 8.10833 10.3175 8.37289 10.6725 8.37289C11.0275 8.37289 11.2988 8.10833 11.2988 7.76225V6.70387H13.5956V9.48248C13.7547 9.47015 13.9155 9.46387 14.0778 9.46387C14.3388 9.46387 14.5959 9.48011 14.8483 9.51163V6.07314C14.8483 5.72707 14.5769 5.46251 14.2219 5.46251L11.2988 5.46265V3.93606ZM7.5405 1.45243C8.91853 1.45243 10.046 2.57202 10.046 3.93572V5.4623H5.03498V3.93572C5.03498 2.57188 6.16246 1.45243 7.5405 1.45243Z"
                  fill="#fff"></Path>
                <Path
                  d="M14.0773 12.5402C13.6997 12.5402 13.3936 12.8462 13.3936 13.2239V14.9334H11.6841C11.3065 14.9334 11.0004 15.2394 11.0004 15.6171C11.0004 15.9947 11.3065 16.3008 11.6841 16.3008H13.3936V18.0103C13.3936 18.3879 13.6997 18.694 14.0773 18.694C14.455 18.694 14.761 18.3879 14.761 18.0103V16.3008H16.4705C16.8482 16.3008 17.1542 15.9947 17.1542 15.6171C17.1542 15.2394 16.8482 14.9334 16.4705 14.9334H14.761V13.2239C14.761 12.8462 14.455 12.5402 14.0773 12.5402Z"
                  fill="#fff"></Path>
              </Svg>
            </View>
          </Pressable>
        </LinearGradient>
      </View>
    </View >

    //  </View>
  );
}

const styles = StyleSheet.create({
  cardContainerHalf: {
    width: width * 0.5,
    height: "auto",
    borderWidth: 0,
    borderColor: 'blue',
    backgroundColor: 'rgb(254,254,255)',
    borderRadius: widthPixel(13),
    // marginHorizontal: 5,
    marginVertical: heightPixel(10),
    elevation: 2,
    shadowRadius: widthPixel(2),
    shadowColor: '#000',
    shadowOpacity: 0.1, // Adjust shadow visibility
    shadowOffset: { width: 0, height: widthPixel(2) },
    overflow: 'hidden',
    borderWidth: 0,
  },

  cardContainer: {
    width: '50%',
    // width: width * 0.5 - 25,
    height: "auto",
    borderWidth: 0,
    borderColor: 'blue',
    backgroundColor: 'rgb(254,254,255)',
    borderRadius: widthPixel(13),
    // marginHorizontal: 5,
    marginVertical: heightPixel(10),
    paddingTop: 5,
    elevation: 2,
    shadowRadius: widthPixel(2),
    shadowColor: '#000',
    shadowOpacity: 0.1, // Adjust shadow visibility
    shadowOffset: { width: 0, height: widthPixel(2) },
    borderWidth: 0,
    // backgroundColor :'red',
    // flexGrow: 1,
  },
  imageContainer: {
    height: 230,
    // marginHorizontal: widthPixel(5),
    overflow: 'hidden',
    borderWidth: 0,
    alignSelf: 'center',
    width: "95%",
    paddingBottom: 0,
    borderRadius: widthPixel(12),
    position: 'relative',
    zIndex: -10
  },
  image: {
    width: '100%',
    height: '100%', // The image takes the full height of its container
    // borderRadius: widthPixel(15),
    resizeMode: 'cover', // Adjust this property according to your image aspect ratio requirement
  },
  bestSellerIcon: {
    position: 'absolute',
    top: -7,
    left: 21,
  },
  wishlistIcon: {
    position: 'absolute',
    top: heightPixel(5),
    right: heightPixel(5),
    width: widthPixel(30), // Size to create the circle
    height: widthPixel(30),
    borderRadius: widthPixel(15),// Half of the width/height to make it circular
    justifyContent: 'center',
    alignItems: 'center',
  },
  variantSelector: {
    position: 'absolute',
    bottom: 8,
    left: 5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  variantSelectorText: {
    color: 'white',
    zIndex: 1,
    position: 'relative',
    top: variantSelectionPosition(width),
    fontSize: fonts._9,
    fontFamily: fonts.FONT_FAMILY.Regular,
    textAlign: 'center',
  },
  tagContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: heightPixel(5),
  },
  variantsTag: {
    fontSize: fonts._11,
    color: '#221f20',
    fontFamily: 'Jost-Regular',
    lineHeight: heightPixel(18),
    paddingHorizontal: widthPixel(5),
  },
  productId: {
    // Add styles for product ID if needed
  },
  productTitle: {
    lineHeight: heightPixel(16),
    paddingHorizontal: widthPixel(5),
    fontFamily: fonts.FONT_FAMILY.Medium,
    fontSize: fonts._12,
    color: '#000',
    borderWidth: 0,
    marginBottom: heightPixel(15),
    overflow: 'hidden',
  },
  bottomContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  productPrice: {
    fontFamily: fonts.FONT_FAMILY.SemiBold,
    fontSize: fonts._14,
    lineHeight: heightPixel(20),
    marginTop: heightPixel(4),
    paddingLeft: 10,
  },
  cartIcon: {
    width: width * 0.117,
    height: height * 0.048,
    borderTopStartRadius: widthPixel(10),
    borderBottomEndRadius: widthPixel(10),
  },
  closeBtnContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingHorizontal: widthPixel(10),
  },
  closeBtn: {
    width: width * 0.098,
    height: height * 0.048,
    alignItems: 'center',
    backgroundColor: 'white',
    elevation: 5,
    borderRadius: widthPixel(20),
  },
  closeIcon: {
    position: 'absolute',
    top: 4,
  },
  // modal varient
  mainBox: {
    paddingHorizontal: widthPixel(10),
    width: width * 0.25,
    flexDirection: 'column',
    alignItems: 'center',
    paddingVertical: heightPixel(10),
    overflow: 'hidden',
  },

  text: {
    fontSize: fonts._16,
    color: 'black',
  },
});
