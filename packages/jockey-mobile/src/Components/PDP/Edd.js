import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  Image,
  ActivityIndicator,
} from 'react-native';
import React, { useEffect, useState } from 'react';
import { ScrollView } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Spinner from '../common/spinner';
import { Pressable } from 'react-native';
import { fonts, heightPixel, widthPixel } from '../../styles';
import { getSettings } from '../../../config';
import { useOrders, useCurrentUser, useUser } from '@appmaker-xyz/shopify';
import AsyncStorage from '@react-native-async-storage/async-storage';
export default function Edd({ variants, selectedVariant }) {
  const [pin, setPin] = useState();
  const [pinText, setPinText] = useState('');
  const [loading, setLoading] = useState(false);
  const [finalEdd, setFinalEdd] = useState('');
  const settings = getSettings();

  // Get current user and orders
  const { id: currentUserId } = useCurrentUser({}) || {};
  const { orderList, isLoading: ordersLoading, refetch: refetchOrders } = useOrders({
    limit: 1
  });
console.log("order", orderList)
  // Enhanced order debugging
  console.log("📋 ORDER LIST DEBUG:");
  console.log("Orders loading:", ordersLoading);
  console.log("Order list length:", orderList?.length);
  console.log("Full order data:", JSON.stringify(orderList, null, 2));
  const extractProductVariantId = variants.map((value) => {
    return { variantId: value.node.id.split('/ProductVariant/')[1] };
  });

  // Function to save pincode to local storage with timestamp (ISO format like order)
  const savePincodeToStorage = async (pincode) => {
    try {
      const pincodeData = {
        pincode: pincode,
        timestamp: new Date().toISOString() // Save in ISO format like order processedAt
      };
      await AsyncStorage.setItem('lastUsedPincode', JSON.stringify(pincodeData));
      console.log('Pincode saved to storage with ISO timestamp:', pincodeData);
    } catch (error) {
      console.error('Error saving pincode to storage:', error);
    }
  };

  // Function to get pincode from local storage
  const getPincodeFromStorage = async () => {
    try {
      const storedData = await AsyncStorage.getItem('lastUsedPincode');
      if (storedData) {
        try {
          const parsedData = JSON.parse(storedData);

          // Check if timestamp is in old Unix format (number) and migrate to ISO format
          if (typeof parsedData.timestamp === 'number') {
            console.log('Migrating Unix timestamp to ISO format');
            const migratedData = {
              pincode: parsedData.pincode,
              timestamp: new Date(parsedData.timestamp).toISOString()
            };
            await AsyncStorage.setItem('lastUsedPincode', JSON.stringify(migratedData));
            console.log('Migrated pincode data:', migratedData);
            return migratedData;
          }

          // Return object with pincode and timestamp (already in ISO format)
          return parsedData;
        } catch (parseError) {
          // Handle very old format (just string) - migrate to new format
          console.log('Migrating very old pincode format (string only) to new format');
          const pincodeData = {
            pincode: storedData,
            timestamp: new Date().toISOString()
          };
          await AsyncStorage.setItem('lastUsedPincode', JSON.stringify(pincodeData));
          return pincodeData;
        }
      }
      return null;
    } catch (error) {
      console.error('Error getting pincode from storage:', error);
      return null;
    }
  };

  // Function to extract pincode with priority based on recency (timestamp)
  const prefillPincode = async (forceRefreshOrders = false) => {
    try {
      console.log('🚀 === PREFILL PINCODE DEBUG START ===');
      console.log('Current user ID:', currentUserId);
      console.log('Orders loading:', ordersLoading);
      console.log('Order list:', orderList);
      console.log('Order list length:', orderList?.length);

      let pincodeToUse = null;
      let source = '';

      // Optionally refresh orders if requested
      if (forceRefreshOrders && refetchOrders) {
        console.log('🔄 Refreshing orders data...');
        try {
          await refetchOrders();
        } catch (error) {
          console.log('Error refreshing orders:', error);
        }
      }

      // Get stored pincode data (with timestamp)
      const storedPincodeData = await getPincodeFromStorage();
      console.log('💾 Stored pincode data:', storedPincodeData);

      // Get order pincode and its timestamp (if user is logged in)
      let orderPincode = null;
      let orderTimestamp = null;

      console.log('🔍 CHECKING ORDER CONDITIONS:');
      console.log('- currentUserId exists:', !!currentUserId);
      console.log('- ordersLoading:', ordersLoading);
      console.log('- orderList exists:', !!orderList);
      console.log('- orderList length:', orderList?.length);

      if (currentUserId && !ordersLoading && orderList && orderList.length > 0) {
        console.log('✅ ORDER CONDITIONS MET - Processing order data');
        const latestOrder = orderList[0]?.node;
        console.log('📦 DETAILED ORDER ANALYSIS:');
        console.log('Total orders found:', orderList.length);
        console.log('Latest order ID:', latestOrder?.id);
        console.log('Latest order number:', latestOrder?.orderNumber || latestOrder?.name);
        console.log('Order processedAt (raw):', latestOrder?.processedAt);
        console.log('Order createdAt (raw):', latestOrder?.createdAt);
        console.log('Full shipping address object:', JSON.stringify(latestOrder?.shippingAddress, null, 2));
        console.log('Shipping address zip:', latestOrder?.shippingAddress?.zip);
        console.log('Shipping address postalCode:', latestOrder?.shippingAddress?.postalCode);
        console.log('Shipping address zipCode:', latestOrder?.shippingAddress?.zipCode);
        console.log('Shipping address pincode:', latestOrder?.shippingAddress?.pincode);

        // Check for pincode in different possible field names
        console.log('🔍 SEARCHING FOR PINCODE IN ORDER:');
        const zipField = latestOrder?.shippingAddress?.zip;
        const postalCodeField = latestOrder?.shippingAddress?.postalCode;
        const zipCodeField = latestOrder?.shippingAddress?.zipCode;
        const pincodeField = latestOrder?.shippingAddress?.pincode;

        console.log('- zip field:', zipField);
        console.log('- postalCode field:', postalCodeField);
        console.log('- zipCode field:', zipCodeField);
        console.log('- pincode field:', pincodeField);

        const possiblePincode = zipField || postalCodeField || zipCodeField || pincodeField;
        console.log('- Final possiblePincode:', possiblePincode);

        if (possiblePincode) {
          orderPincode = possiblePincode;
          // Keep order timestamp in ISO format (no conversion needed)
          orderTimestamp = latestOrder.processedAt || latestOrder.createdAt || null;
          console.log('✅ FOUND ORDER PINCODE:', orderPincode);
          console.log('Order processedAt (ISO):', latestOrder.processedAt);
          console.log('Order createdAt (ISO):', latestOrder.createdAt);
          console.log('Selected order timestamp (ISO):', orderTimestamp);
        } else {
          console.log('❌ NO PINCODE FOUND IN ORDER - checked all possible fields');
        }
      } else {
        console.log('❌ ORDER CONDITIONS NOT MET:');
        console.log('- currentUserId:', currentUserId);
        console.log('- ordersLoading:', ordersLoading);
        console.log('- orderList exists:', !!orderList);
        console.log('- orderList length:', orderList?.length);
        console.log('- Reason: User not logged in, orders still loading, or no orders found');
      }

      console.log('🎯 FINAL DECISION MAKING:');
      console.log('- storedPincodeData:', storedPincodeData);
      console.log('- orderPincode:', orderPincode);
      console.log('- orderTimestamp:', orderTimestamp);
      console.log('- Both exist?', !!(storedPincodeData && orderPincode));

      // Enhanced debugging for timestamp comparison (both in ISO format now)
      if (storedPincodeData && orderPincode && orderTimestamp) {
        console.log('🔍 TIMESTAMP COMPARISON DEBUG (ISO FORMAT):');
        console.log('Order timestamp (ISO):', orderTimestamp);
        console.log('Saved timestamp (ISO):', storedPincodeData.timestamp);

        // Convert both to Date objects for comparison
        const orderDate = new Date(orderTimestamp);
        const savedDate = new Date(storedPincodeData.timestamp);

        console.log('Order Date object:', orderDate);
        console.log('Saved Date object:', savedDate);
        console.log('Order > Saved?', orderDate > savedDate);
        console.log('Difference (ms):', orderDate.getTime() - savedDate.getTime());
        console.log('Difference (hours):', (orderDate.getTime() - savedDate.getTime()) / (1000 * 60 * 60));
      }

      // PRIORITY LOGIC: Always use the most recent pincode based on timestamp comparison
      if (storedPincodeData && orderPincode) {
        // Both exist, compare timestamps to use the most recent one
        if (orderTimestamp) {
          // Convert both ISO timestamps to Date objects for comparison
          const orderDate = new Date(orderTimestamp);
          const savedDate = new Date(storedPincodeData.timestamp);
          const timeDifferenceMinutes = (savedDate.getTime() - orderDate.getTime()) / (1000 * 60);

          console.log('🔍 PRIORITY DECISION LOGIC:');
          console.log('Order timestamp (ISO):', orderTimestamp);
          console.log('Saved timestamp (ISO):', storedPincodeData.timestamp);
          console.log('Time difference (minutes):', timeDifferenceMinutes);
          console.log('Which is more recent:', timeDifferenceMinutes > 0 ? 'Saved pincode' : 'Order pincode');

          // Use whichever pincode is more recent
          if (timeDifferenceMinutes > 0) {
            // Saved pincode is more recent
            pincodeToUse = storedPincodeData.pincode;
            source = 'local storage (more recent)';
            console.log('✅ Using stored pincode as it is more recent:', pincodeToUse);
          } else {
            // Order pincode is more recent or same time
            pincodeToUse = orderPincode;
            source = 'latest order (more recent)';
            console.log('✅ Using order pincode as it is more recent:', pincodeToUse);
          }
        } else {
          // No valid order timestamp, use stored pincode
          pincodeToUse = storedPincodeData.pincode;
          source = 'local storage (no valid order timestamp)';
          console.log('✅ Using stored pincode (no valid order timestamp):', pincodeToUse);
        }
      } else if (storedPincodeData) {
        // Only stored pincode exists
        pincodeToUse = storedPincodeData.pincode;
        source = 'local storage';
        console.log('✅ Using stored pincode (only option):', pincodeToUse);
      } else if (orderPincode) {
        // Only order pincode exists
        pincodeToUse = orderPincode;
        source = 'latest order';
        console.log('✅ Using order pincode (only option):', pincodeToUse);
      } else {
        // Neither exists, use default
        pincodeToUse = '560103';
        source = 'default';
        console.log('✅ No pincode found, using default pincode:', pincodeToUse);
      }

      console.log('🏁 FINAL RESULT:');
      console.log('- Selected pincode:', pincodeToUse);
      console.log('- Source:', source);
      console.log('- Current pin in input:', pin);
      console.log('- Will update input?', pincodeToUse !== pin);

      // Only proceed if pincode is different from current pin or pin is empty
      if (pincodeToUse !== pin) {
        console.log('✅ UPDATING PINCODE INPUT:', pincodeToUse);
        // Set the pincode in the input field
        setPin(pincodeToUse);

        // Save to local storage if it came from order (to maintain timestamp tracking)
        // Do NOT save default pincode to storage
        if (source.includes('latest order') && source !== 'default') {
          console.log('💾 Saving order pincode to storage for future reference');
          await savePincodeToStorage(pincodeToUse);
        }

        // Show loading state and automatically trigger EDD check
        setLoading(true);
        fetchRequestEDD(pincodeToUse);
      } else {
        console.log('⏭️ SKIPPING UPDATE - Pincode unchanged');
      }

      console.log('🚀 === PREFILL PINCODE DEBUG END ===');
    } catch (error) {
      console.error('❌ Error prefilling pincode:', error);
      setLoading(false);
    }
  };

  // State to track if we've already attempted to prefill
  const [hasPrefilled, setHasPrefilled] = useState(false);

  useEffect(() => {
    console.log('🔄 Component mounted - initializing');
    setLoading(false);
    // Reset prefill state when component mounts (new product)
    setHasPrefilled(false);
  }, []);

  // Effect to prefill pincode when user and orders are loaded (only once)
  useEffect(() => {
    if (currentUserId && !ordersLoading && !hasPrefilled) {
      prefillPincode();
      setHasPrefilled(true);
    }
  }, [currentUserId, ordersLoading, hasPrefilled]);

  // Add a method to manually refresh and re-evaluate pincode (useful after order placement)
  const refreshPincodeFromOrders = async () => {
    console.log('🔄 Manually refreshing pincode from orders...');
    setHasPrefilled(false);
    await prefillPincode(true); // Force refresh orders
    setHasPrefilled(true);
  };

  // Effect to prefill pincode for guest users (from local storage only)
  useEffect(() => {
    console.log('👤 Guest user effect triggered:', {
      currentUserId: !!currentUserId,
      hasPrefilled,
      shouldTrigger: !currentUserId && !hasPrefilled
    });

    if (!currentUserId && !hasPrefilled) {
      console.log('🚀 Triggering prefill for guest user');
      prefillPincode();
      setHasPrefilled(true);
    }
  }, [currentUserId, hasPrefilled]);

  function handleDeliveryDetailbutton() {
    setLoading(true);

    if (!pin || pin.length !== 6) {
      setPinText('Please Enter a valid Pin');
      setLoading(false);
    } else {
      fetchRequestEDD(pin);
      setPinText('');
    }
  }

  const fetchRequestEDD = async (pinData) => {
    const response = await fetch(
      settings.edd_api_url,
      {
        method: 'POST',
        headers: {
          accept: 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          dropPincode: pinData,
          quantity: 1,
          variantsData: extractProductVariantId,
        }),
      },
    );

    const responseData = await response.json();
    console.log(responseData, " response data")
    if (responseData.serviceable) {
      if (responseData?.variantsData?.length)
        setFinalEdd(responseData?.variantsData?.[0]?.edd);
      setPinText('');
      setLoading(false);

      // Save pincode to storage only when it's valid and serviceable
      // Do NOT save default pincode (560103) to storage
      if (pinData && pinData.length === 6 && pinData !== '560103') {
        savePincodeToStorage(pinData);
        console.log('Pincode saved after successful validation:', pinData);
      } else if (pinData === '560103') {
        console.log('Default pincode validated but not saved to storage:', pinData);
      }
    } else {
      setLoading(false);
      setFinalEdd('');
      setPinText('Pincode not serviceable.');
    }
  };

  return (
    <View>
      <View
        style={{
          borderWidth: 0,
          elevation: 0,
          paddingVertical: heightPixel(8),
          borderRadius: widthPixel(10),
        }}>
        <Text
          style={{
            color: '#221f20',
            fontSize: fonts._16,
            fontFamily: fonts.FONT_FAMILY.SemiBold,
            marginBottom: heightPixel(10),
          }}>
          Delivery Details
        </Text>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            overflow: 'hidden',
            gap: widthPixel(8),
            flexWrap: 'wrap',
          }}>
          <View style={{ width: '60%' }}>
            <TextInput
              placeholder="Enter Your pincode"
              maxLength={6}
              keyboardType="numeric"
              onChangeText={(newPin) => {
                if (newPin === '') {
                  setPinText('');
                }
                const trimmedPin = newPin.trim();
                setPin(trimmedPin);
              }}
              value={pin}
              style={{
                borderWidth: 1,
                borderColor: 'rgba(34,31,32,.1)',
                padding: widthPixel(8),
                borderRadius: widthPixel(8),
                fontFamily: 'Jost-SemiBold',
                paddingLeft: widthPixel(20),
                height: heightPixel(50),
                alignItems: 'center', justifyContent: 'center',
              }}
            />
          </View>
          <View style={{ width: '32%' }}>
            <Pressable
              onPress={() => {
                handleDeliveryDetailbutton();
              }}>
              <LinearGradient
                colors={['#221f20', '#505050']}
                start={{ x: -0.3336, y: 0 }} // Adjusted based on the angle in the CSS
                end={{ x: 1.3952, y: 1 }} // Adjusted based on the angle in the CSS
                style={{
                  paddingHorizontal: widthPixel(10),
                  // paddingVertical: heightPixel(13),
                  height: heightPixel(50),
                  alignItems: 'center', justifyContent: 'center',
                  borderRadius: widthPixel(10),
                }}>
                {!loading ? (
                  <Text style={styles.buttonText}>CHECK</Text>
                ) : (
                  <View
                    style={{
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}>
                    {/* <Spinner /> */}
                    <ActivityIndicator size={'small'} color={'white'} />
                  </View>
                )}
              </LinearGradient>
            </Pressable>
          </View>
        </View>

        {pinText != '' && (
          <View style={{ paddingVertical: heightPixel(10) }}>
            <Text
              style={{ color: 'red', fontFamily: fonts.FONT_FAMILY.Regular }}>
              {pinText}
            </Text>
          </View>
        )}

        {/* EDD details , div needs to be visible when pincode entered  */}
        {finalEdd ? (
          <View>
            <ScrollView
              horizontal={true}
              style={{
                paddingVertical: heightPixel(10),
                borderWidth: 0,
                marginVertical: heightPixel(10),
                width: '100%',
              }}
              contentContainerStyle={{
                paddingRight: widthPixel(50),
              }}>
              <View style={{ display: 'flex', flexDirection: 'row' }}>
                <View
                  style={{
                    padding: widthPixel(10),
                    borderRadius: widthPixel(7),
                    borderWidth: 1,
                    borderColor: 'rgba(34,31,32,.1)',
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    width: '60%',
                    marginRight: widthPixel(10),
                    height: heightPixel(50),
                  }}>
                  <View
                    style={{ width: widthPixel(20), height: widthPixel(20) }}>
                    <Image
                      source={{
                        uri: 'https://www.jockey.in/cdn/shop/files/Group_239103.jpg?v=16732760107086212298',
                      }}
                      style={{
                        width: '100%',
                        height: '100%',
                        resizeMode: 'contain',
                      }}
                    />
                  </View>
                  <View>
                    <Text
                      style={{
                        fontSize: fonts._14,
                        fontFamily: fonts.FONT_FAMILY.Regular,
                      }}>
                      Estimated Delivery by{' '}
                      <Text
                        style={{
                          fontSize: fonts._13,
                          fontFamily: fonts.FONT_FAMILY.Bold,
                        }}>
                        {finalEdd}
                      </Text>
                    </Text>
                  </View>
                </View>

                {/* 3 */}
                <View
                  style={{
                    padding: widthPixel(10),
                    borderRadius: widthPixel(7),
                    borderWidth: 1,
                    borderColor: 'rgba(34,31,32,.1)',
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    width: '40%',
                  }}>
                  <View
                    style={{ width: widthPixel(20), height: widthPixel(20) }}>
                    <Image
                      source={{
                        uri: 'https://www.jockey.in/cdn/shop/files/Vector_1_a29858f8-e63b-4de4-9be6-3f78849099e3.png?v=9477741078544455416',
                      }}
                      style={{
                        width: '100%',
                        height: '100%',
                        resizeMode: 'contain',
                      }}
                    />
                  </View>
                  <Text
                    style={{
                      fontSize: fonts._13,
                      fontFamily: fonts.FONT_FAMILY.Regular,
                    }}>
                    {'Eligible for Free Delivery'}
                  </Text>
                </View>
              </View>
            </ScrollView>
          </View>
        ) : (
          <View></View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  buttonText: {
    fontSize: fonts._14,
    color: 'white',
    textAlign: 'center',
    fontFamily: fonts.FONT_FAMILY.Regular,
  },
});
