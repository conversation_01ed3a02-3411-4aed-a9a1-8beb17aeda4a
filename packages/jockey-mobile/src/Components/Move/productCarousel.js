import { Dimensions, Image, StyleSheet, Text, View } from 'react-native'
import React, { useRef, useState } from 'react'
import { fontPixel, fonts, getHorizontalPadding, heightPixel, widthPixel } from '../../styles'
import Svg, { Path } from 'react-native-svg';
import { useProducts } from '@appmaker-xyz/shopify';
import ProductCard from '../common/ProductCard';
import Carousel, { Pagination } from 'react-native-snap-carousel';

const { width } = Dimensions.get('window');

const productCarousel = (props) => {

    const rawId = props?.attributes?.collectionId["0"];
    const { productList } = useProducts({
        query: { collectionId: rawId },
        limit: 10,
    });

    const carouselRef = useRef(null);
    const [carouselKey, setCarouselKey] = useState(0);
    const [activeIndex, setActiveIndex] = useState(0);


    return (

        <View style={{ flex: 1, paddingHorizontal: widthPixel(6), marginTop: heightPixel(50) }} >

            <Text style={{
                fontSize: fonts._32,
                marginBottom: heightPixel(5),
                fontFamily: "Jost-Medium",
                color: "#000",
                fontWeight: "500",
                letterSpacing: -0.8
            }} >{props?.attributes?.Header}</Text>

            <Text style={{ fontSize: fonts._16, fontFamily: "Jost-Regular", letterSpacing: -0.8, fontWeight: "400", marginBottom: heightPixel(27) }}
            >{props?.attributes?.Header2}</Text>

            <View style={{
                width: "100%",
                height: heightPixel(250),
                backgroundColor: props?.attributes?.themeFontColor,
                paddingHorizontal: widthPixel(15),
                paddingTop: heightPixel(15),
                borderRadius: widthPixel(8),
            }} >

                <Text style={{ fontSize: fonts._24, fontFamily: "Jost-SemiBold" }} >{props?.attributes?.MainTitle}</Text>

                <View style={{
                    flexDirection: "row", justifyContent: "space-between", marginTop: heightPixel(10)
                }} >

                    < View style={{
                        alignItems: "flex-start",
                        justifyContent: "space-between",
                        flex: 1
                    }} >

                        <View style={{
                            flexDirection: "row",
                            justifyContent: "space-between",
                            width: "100%"
                        }} >

                            <View style={{ padding: widthPixel(10), backgroundColor: "white", borderRadius: 25, fontFamily: "Jost-Light" }} >
                                <Text style={{ fontSize: fontPixel(10) }} >{props?.attributes?.Filter1}</Text>
                            </View>
                            <View style={{ padding: widthPixel(10), backgroundColor: "white", borderRadius: 25 }} >
                                <Text style={{ fontSize: fontPixel(10) }} >{props?.attributes?.Filter2}</Text>
                            </View>
                            <View style={{ padding: widthPixel(10), backgroundColor: "white", borderRadius: 25 }} >
                                <Text style={{ fontSize: fontPixel(10) }} >{props?.attributes?.Filter3}</Text>
                            </View>

                        </View>

                        <View style={{ flexDirection: "row", justifyContent: "center", alignItems: "center" }} >

                            <Text style={{ color: "black", opacity: 0.35, fontSize: fonts._12, fontWeight: "700", fontFamily: "Jost-Bold" }} >EXPLORE ALL</Text>

                            {getHorizontalPadding(6)}

                            <Svg xmlns="http://www.w3.org/2000/svg" width="9" height="12" viewBox="0 0 9 12" fill="none">
                                <Path d="M4.5 -1.5299e-07L4.5 11M4.5 11L8 7.5M4.5 11L1 7.5" stroke="black"></Path>
                            </Svg>

                        </View>


                    </View>


                    <Image style={{ height: heightPixel(82), width: widthPixel(77), marginTop: -10 }} source={{ uri: props?.attributes?.sideimage?.url }} />

                </View>

            </View >


            <View>

                <Carousel
                    ref={carouselRef}
                    key={carouselKey}
                    layout={'default'}
                    loop={false}
                    // data={props?.innerBlocks.slice(0, 1)}
                    data={productList}
                    sliderWidth={width}
                    itemWidth={widthPixel(200)}
                    activeSlideAlignment="start"
                    renderItem={({ item, index }) => {

                        return (
                            <ProductCard
                                key={item?.node?.handle}
                                onClick={{}}
                                title={item?.node?.title}
                                imageUrl={item?.node?.images}
                                regularPrice={item?.node?.priceRange?.minVariantPrice}
                                variationOptions={item?.node?.options}
                                productNode={item?.node}
                                props={props}
                                productVariantDetails={item?.node?.color_variant.references}
                            />
                        )
                    }}
                    containerCustomStyle={{
                        marginLeft: widthPixel(18),
                        marginTop: -120
                    }}
                    inactiveSlideOpacity={1}
                    inactiveSlideScale={1}
                    onSnapToItem={(index) => setActiveIndex(index)}
                />

            </View>

        </View >

    )

}

export default productCarousel

const styles = StyleSheet.create({})