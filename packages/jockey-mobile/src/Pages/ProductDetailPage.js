var sbHeight = window.innerHeight;

const gql = `
query GetProductDetails($handle: String) {
  product(handle: $handle) {
    return_policy: metafield(key: "return_policy_", namespace: "custom") {
      __typename
      value
    }

    style_note: metafield(key: "style_note", namespace: "custom") {
      __typename
      value
    }

    style_note_image: metafield(key: "style_note_image", namespace: "custom") {
      __typename
      value
    }

    product_description_image_dev: metafield(key: "product_description_image", namespace: "custom") {
      __typename
      value
    }

    product_description_image_prod: metafield(key: "product_decription_image", namespace: "custom") {
      __typename
      value
    }

    product_fit_and_feel_image: metafield(key: "product_fit_and_feel_image", namespace: "custom") {
      __typename
      value
    }

    product_fit_and_feel: metafield(key: "product_fit_and_feel", namespace: "custom") {
      __typename
      value
    }

    product_is_ideal_for_image: metafield(key: "product_is_ideal_for_image", namespace: "custom") {
      __typename
      value
    }

    product_is_ideal_for_sub_text: metafield(key: "product_is_ideal_for_sub_text", namespace: "custom") {
      __typename
      value
    }

    washing_instruction_image: metafield(key: "washing_instruction_image", namespace: "custom") {
      __typename
      value
    }

    washing_instructions_text: metafield(key: "washing_instructions_text", namespace: "custom") {
      __typename
      value
    }

    disclaimer_text: metafield(key: "disclaimer_text", namespace: "custom") {
      __typename
      value
    }

    shopthelook_hotspot_products_links: metafield(key: "shopthelook_hotspot_products_links", namespace: "custom") {
      __typename
      value
    }

    shopthelook_hotspot_coordinates_desk_mob: metafield(key: "shopthelook_hotspot_coordinates_desk_mob", namespace: "custom") {
      __typename
      value
    }

    shopthelook_nav_button: metafield(key: "shopthelook_nav_button", namespace: "custom") {
      __typename
      value
    }

  }
}`


const ProductDetailPage = {
  title: 'Product Detail',
  blocks: [
    {
      name: 'shopify/page-meta-graphql-query',
      attributes: {
        pageStateName: 'product-meta',
        query: gql,
        variables: {
          handle: '{{blockData.node.handle}}',
        },
      },
    },
    {
      name: 'appmaker/shopify-product-image',
    },
    {
      name: 'appmaker/shopify-variation-listner',
      attributes: {},
    },
    {
      name: 'appmaker/custom-size-variation',
      attributes: {},
    },
    {
      name: 'appmaker/shopify-product-variation',
      attributes: {},
    },

    //  {
    //   name: "appmaker/product-counter",
    //   attributes: {
    //     counter: true,
    //     title: "Quantity",
    //     __appmakerStylesClassName: "customProductDetailCounterBlock",
    //   },
    // },
    {
      name: 'appmaker/size-chart',
      attributes: {},
    },

    // {
    //   name: 'appmaker/shop-the-look',
    //   attributes: {},
    // },

    {
      name: 'appmaker/shopify-product-data',
      clientId: 'product-data',
      attributes: {},
    },

    {
      name: 'appmaker/meta-fields-value',
      clientId: 'custom-related-products',
      attributes: {
        productIds: '{{blockItem.node.custom_cites}}',
      },
    },
    // {
    //   name: 'appmaker/shop-the-look',
    //   attributes: {},
    // },

    // {
    //   name: 'appmaker/shop-the-look',
    //   attributes: {},
    // },

    {
      name: 'appmaker/shopify-variation-listner',
      clientId: 'de9b066c-ae0d-4f7c-a3dd-123',
      attributes: {},
    },

    //  {
    //    name: "appmaker/shopify-product-description",
    //    attributes: {},
    //  },
    // {
    //   name:'appmaker/custom-block',
    //   attributes:{}
    // }
  ],
  stickyFooter: {
    blocks: [
      {
        name: 'appmaker/custom-pdp-product',
      },
      //  {
      //    clientId: 'custom-theme-name-custom-pdp-buttons',
      //    name: 'appmaker/custom-pdp-buttons',
      //    attributes: {},
      //  },
    ],
  },
};
export default ProductDetailPage;
