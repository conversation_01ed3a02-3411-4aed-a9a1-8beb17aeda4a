import { Image, StyleSheet, Text, View, Animated } from 'react-native';
import React, { useEffect, useRef } from 'react';
import { fonts, getVerticalPadding, heightPixel, widthPixel } from '../../styles';

const MensMove = (props) => {

    const opacity = useRef(new Animated.Value(1)).current;

    useEffect(() => {
        const blinkAnimation = Animated.loop(
            Animated.sequence([
                Animated.timing(opacity, {
                    toValue: 0,
                    duration: 500,
                    useNativeDriver: true,
                }),
                Animated.timing(opacity, {
                    toValue: 1,
                    duration: 500,
                    useNativeDriver: true,
                }),
            ])
        );

        blinkAnimation.start();

        return () => blinkAnimation.stop();
    }, []);

    return (
        <View style={{ height: heightPixel(650), width: "100%" }}>

            <Image
                source={{ uri: props?.innerBlocks?.[0]?.attributes?.blockimage?.url }}
                style={{ height: heightPixel(600), width: "100%" }}
                resizeMode='cover'
            />

            <View style={styles.blinkContainer}>

                <Animated.Text style={[styles.blinkText, { opacity }]}>
                    Scroll down to explore
                </Animated.Text>

                {getVerticalPadding(2)}

                <Animated.Image
                    source={require("../../assets/images/ArrowDown.png")}
                    style={[styles.arrowImage, { opacity }]}
                    resizeMode='cover'
                />
            </View>

        </View>
    );
};

export default MensMove;

const styles = StyleSheet.create({
    blinkContainer: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
    },
    blinkText: {
        fontSize: fonts._11,
        fontFamily: "Jost-Regular",
        color: "#4C4B4D",
    },
    arrowImage: {
        height: heightPixel(13),
        width: widthPixel(13),
    },
});
