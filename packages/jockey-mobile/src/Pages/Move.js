import { Pressable, StyleSheet, Text, View, Animated, TouchableOpacity, ScrollView, FlatList } from 'react-native'
import React, { useEffect, useRef, useState } from 'react'
import { BlockScreen } from '@appmaker-xyz/react-native';
import { fonts, heightPixel, widthPixel } from '../styles';

const Move = (props) => {

    let { attributes, innerBlocks, clientId } = props;
    const [activeCategory, setActiveCategory] = useState('mensmove');
    const [isMen, setIsMen] = useState(true);
    const translateX = useRef(new Animated.Value(0)).current;
    const [isToggling, setIsToggling] = useState(false);

    const toggleWidth = 50;
    const padding = 5;
    const ballWidth = widthPixel(14);
    const circleDistance = toggleWidth - 2 * padding - ballWidth;

    const animatedColor = translateX.interpolate({
        inputRange: [0, circleDistance],
        outputRange: ['#428bc1', '#f83f75'],
        extrapolate: 'clamp',
    });

    const handleToggle = () => {
        if (isToggling) return;

        setIsToggling(true);

        const toValue = isMen ? circleDistance : 0;

        Animated.timing(translateX, {
            toValue,
            duration: 200,
            useNativeDriver: true,
        }).start(() => {
            setIsMen(prev => !prev);
            setActiveCategory(isMen ? 'BftyHmkwGD' : 'mensmove');
            setIsToggling(false);
        });
    };

    return (

        <View style={{ flex: 1 }} >


            <FlatList
                showsVerticalScrollIndicator={false}
                data={[0]}
                renderItem={() => {

                    return (

                        <>

                            <View style={{ height: heightPixel(95), padding: widthPixel(13) }} >

                                <View
                                    style={{
                                        height: heightPixel(70),
                                        borderWidth: 1,
                                        borderColor: '#E4E4E4',
                                        borderRadius: 100,
                                        flexDirection: 'row',
                                        justifyContent: 'space-evenly',
                                        alignItems: 'center',
                                    }}
                                >
                                    <View style={{ alignItems: 'center' }}>
                                        <Text
                                            style={{
                                                fontFamily: 'Jost-SemiBold',
                                                fontWeight: '500',
                                                fontSize: fonts._16,
                                            }}
                                        >
                                            MOVE
                                        </Text>
                                        <Text
                                            style={{
                                                fontFamily: 'Jost-SemiBold',
                                                fontWeight: '500',
                                                color: '#428bc1',
                                            }}
                                        >
                                            MEN
                                        </Text>
                                    </View>

                                    <Pressable
                                        onPress={handleToggle}
                                        style={{
                                            width: toggleWidth,
                                            height: heightPixel(20),
                                            borderRadius: widthPixel(15),
                                            backgroundColor: 'transparent',
                                            justifyContent: 'center',
                                            overflow: 'hidden',
                                            borderWidth: 1,
                                            padding: padding,
                                        }}
                                    >
                                        <Animated.View
                                            style={{
                                                width: ballWidth,
                                                height: ballWidth,
                                                borderRadius: ballWidth / 2,
                                                backgroundColor: animatedColor,
                                                transform: [{ translateX }],
                                            }}
                                        />
                                    </Pressable>

                                    <View style={{ alignItems: 'center' }}>
                                        <Text
                                            style={{
                                                fontFamily: 'Jost-SemiBold',
                                                fontWeight: '500',
                                                fontSize: fonts._16,
                                            }}
                                        >
                                            MOVE
                                        </Text>
                                        <Text
                                            style={{
                                                fontFamily: 'Jost-Regular',
                                                color: '#f83f75',
                                            }}
                                        >
                                            WOMEN
                                        </Text>
                                    </View>
                                </View>

                            </View>

                            <BlockScreen pageId={activeCategory} />


                        </>

                    )

                }}
            />


        </View>

    )

}

export default Move

const styles = StyleSheet.create({})