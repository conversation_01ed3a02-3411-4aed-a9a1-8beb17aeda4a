import { Dimensions, Pressable, StyleSheet, Text, View } from 'react-native'
import React, { useRef, useState } from 'react'
import { fontPixel, fonts, getVerticalPadding, heightPixel, widthPixel } from '../../styles'
import Video from 'react-native-video';
import Svg, { Path } from 'react-native-svg';
import { useProducts } from '@appmaker-xyz/shopify';
import ProductCard from '../common/ProductCard';
import Carousel, { Pagination } from 'react-native-snap-carousel';
import { useViewableItems } from '@appmaker-xyz/react-native';

const { width } = Dimensions.get('window');

const videoBgProductCarousel = (props) => {
    let { attributes, innerBlocks, clientId, onAction } = props;
    const rawId = props?.attributes?.collectionId["0"];
    const { productList } = useProducts({
        query: { collectionId: rawId },
        limit: 10,
    });

    const [isMuted, setIsMuted] = useState(true);
    const videoRef = useRef(null);
    const carouselRef = useRef(null);
    const [carouselKey, setCarouselKey] = useState(0);
    const [activeIndex, setActiveIndex] = useState(0);

    const toggleMute = () => {
        setIsMuted((prev) => !prev);
    };

    useViewableItems({
        enabled: true,
        clientId,
        visibilityListener: (isVisible, index) => {
            console.log("Move Visible", isVisible);
            if (!isVisible) {
                setIsMuted(true)
            }
        },
    });

    function openProduct(handle) {
        onAction({
            action: 'OPEN_PRODUCT',
            params: {
                productHandle: `${handle}`,
            },
        });
    }

    return (

        <View style={{ flex: 1, marginTop: heightPixel(30) }} >

            <View style={{ paddingHorizontal: widthPixel(16) }} >

                <Text style={{ fontSize: fonts._25, fontFamily: "Jost-Bold", fontWeight: "500", color: "#000", letterSpacing: -0.8 }} >{props?.attributes?.title}</Text>
                <Text style={{ fontSize: fonts._16, fontFamily: "Jost-Regular", fontWeight: "400", color: "#000", letterSpacing: -0.8 }} >{props?.attributes?.subTitle}</Text>

            </View>

            {getVerticalPadding(24)}

            <View>
                <Video
                    source={{ uri: props?.attributes?.videoUrl?.url }}
                    ref={videoRef}
                    style={{
                        height: heightPixel(550), borderRadius: widthPixel(23)
                    }}
                    resizeMode="cover"
                    repeat
                    muted={isMuted}
                    controls={false}
                />

                <Pressable onPress={() => toggleMute()} style={{ position: "absolute", right: 30, top: 30 }} >

                    {

                        isMuted ?
                            <Svg width="30px" class="mute1" height="30px" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fill="#FFFFFF">
                                <Path fill-rule="evenodd" clip-rule="evenodd" d="M1.5 5h2.79l3.86-3.83.85.35v13l-.85.33L4.29 11H1.5l-.5-.5v-5l.5-.5zm3.35 5.17L8 13.31V2.73L4.85 5.85 4.5 6H2v4h2.5l.35.17zm9.381-4.108l.707.707L13.207 8.5l1.731 1.732-.707.707L12.5 9.207l-1.732 1.732-.707-.707L11.793 8.5 10.06 6.77l.707-.707 1.733 1.73 1.731-1.731z"></Path>
                            </Svg>

                            :

                            <Svg id="new-unmute-icon" class="active mute222" xmlns="http://www.w3.org/2000/svg" fill="#fff" width="30px" height="30px" viewBox="0 0 24 24">
                                <Path fill-rule="evenodd" d="M11.553 3.064A.75.75 0 0112 3.75v16.5a.75.75 0 01-1.255.555L5.46 16H2.75A1.75 1.75 0 011 14.25v-4.5C1 8.784 1.784 8 2.75 8h2.71l5.285-4.805a.75.75 0 01.808-.13zM10.5 5.445l-4.245 3.86a.75.75 0 01-.505.195h-3a.25.25 0 00-.25.25v4.5c0 .*************.25h3a.75.75 0 01.505.195l4.245 3.86V5.445z">
                                </Path>
                                <Path d="M18.718 4.222a.75.75 0 011.06 0c4.296 4.296 4.296 11.26 0 15.556a.75.75 0 01-1.06-1.06 9.5 9.5 0 000-13.436.75.75 0 010-1.06z">
                                </Path>
                                <Path d="M16.243 7.757a.75.75 0 10-1.061 1.061 4.5 4.5 0 010 6.364.75.75 0 001.06 1.06 6 6 0 000-8.485z"></Path>
                            </Svg>

                    }

                </Pressable>

            </View>

            <View style={{ flexDirection: "row" }} >

                <Carousel
                    ref={carouselRef}
                    key={carouselKey}
                    layout={'default'}
                    loop={false}
                    // data={props?.innerBlocks.slice(0, 1)}
                    data={productList}
                    sliderWidth={width}
                    itemWidth={widthPixel(200)}
                    activeSlideAlignment="start"
                    renderItem={({ item, index }) => {

                        return (
                            <ProductCard
                                key={item?.node?.handle}
                                onClick={() => openProduct(item?.node?.handle)}
                                title={item?.node?.title}
                                imageUrl={item?.node?.images}
                                regularPrice={item?.node?.priceRange?.minVariantPrice}
                                variationOptions={item?.node?.options}
                                productNode={item?.node}
                                props={props}
                                productVariantDetails={item?.node?.color_variant.references}
                            />
                        )
                    }}
                    containerCustomStyle={{
                        marginTop: -200,
                        marginLeft: widthPixel(45)
                    }}
                    inactiveSlideOpacity={1}
                    inactiveSlideScale={1}
                    onSnapToItem={(index) => setActiveIndex(index)}
                />

            </View>


        </View>

    )

}

export default videoBgProductCarousel

const styles = StyleSheet.create({})